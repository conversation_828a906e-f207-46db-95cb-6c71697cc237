{"_from": "base64-arraybuffer@0.1.5", "_id": "base64-arraybuffer@0.1.5", "_inBundle": false, "_integrity": "sha512-437oANT9tP582zZMwSvZGy2nmSeAb8DW2me3y+Uv1Wp2Rulr8Mqlyrv3E7MLxmsiaPSMMDmiDVzgE+e8zlMx9g==", "_location": "/base64-arraybuffer", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "base64-arraybuffer@0.1.5", "name": "base64-arraybuffer", "escapedName": "base64-arraybuffer", "rawSpec": "0.1.5", "saveSpec": null, "fetchSpec": "0.1.5"}, "_requiredBy": ["/engine.io-parser"], "_resolved": "https://registry.npmjs.org/base64-arraybuffer/-/base64-arraybuffer-0.1.5.tgz", "_shasum": "73926771923b5a19747ad666aa5cd4bf9c6e9ce8", "_spec": "base64-arraybuffer@0.1.5", "_where": "D:\\Code\\node-express\\node_modules\\engine.io-parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://hertzen.com"}, "bugs": {"url": "https://github.com/niklasvh/base64-arraybuffer/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Encode/decode base64 data into ArrayBuffers", "devDependencies": {"grunt": "^0.4.5", "grunt-cli": "^0.1.13", "grunt-contrib-jshint": "^0.11.2", "grunt-contrib-nodeunit": "^0.4.1", "grunt-contrib-watch": "^0.6.1"}, "engines": {"node": ">= 0.6.0"}, "homepage": "https://github.com/niklasvh/base64-arraybuffer", "keywords": [], "licenses": [{"type": "MIT", "url": "https://github.com/niklasvh/base64-arraybuffer/blob/master/LICENSE-MIT"}], "main": "lib/base64-arraybuffer", "name": "base64-arraybuffer", "repository": {"type": "git", "url": "git+https://github.com/niklasvh/base64-arraybuffer.git"}, "scripts": {"test": "grunt nodeunit"}, "version": "0.1.5"}