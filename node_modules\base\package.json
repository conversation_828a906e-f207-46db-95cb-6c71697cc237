{"_from": "base@^0.11.1", "_id": "base@0.11.2", "_inBundle": false, "_integrity": "sha512-5T6P4xPgpp0YDFvSWwEZ4NoE3aM4QBQXDzmVbraCkFj8zHM+mba8SyqB5DbZWyR7mYHo6Y7BdQo3MoA4m0TeQg==", "_location": "/base", "_phantomChildren": {"is-accessor-descriptor": "1.0.1", "is-data-descriptor": "1.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "base@^0.11.1", "name": "base", "escapedName": "base", "rawSpec": "^0.11.1", "saveSpec": null, "fetchSpec": "^0.11.1"}, "_requiredBy": ["/snapdragon"], "_resolved": "https://registry.npmjs.org/base/-/base-0.11.2.tgz", "_shasum": "7bde5ced145b6d551a90db87f83c558b4eb48a8f", "_spec": "base@^0.11.1", "_where": "D:\\Code\\node-express\\node_modules\\snapdragon", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/node-base/base/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://twitter.com/doowb"}, {"name": "<PERSON>", "url": "https://github.com/criticalmash"}, {"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "tunnckoCore", "url": "https://i.am.charlike.online"}, {"url": "https://github.com/wtgtybhertgeghgtwtg"}], "dependencies": {"cache-base": "^1.0.1", "class-utils": "^0.3.5", "component-emitter": "^1.2.1", "define-property": "^1.0.0", "isobject": "^3.0.1", "mixin-deep": "^1.2.0", "pascalcase": "^0.1.1"}, "deprecated": false, "description": "base is the foundation for creating modular, unit testable and highly pluggable node.js applications, starting with a handful of common methods, like `set`, `get`, `del` and `use`.", "devDependencies": {"gulp": "^3.9.1", "gulp-eslint": "^4.0.0", "gulp-format-md": "^1.0.0", "gulp-istanbul": "^1.1.2", "gulp-mocha": "^3.0.1", "helper-coverage": "^0.1.3", "mocha": "^3.5.0", "should": "^13.0.1", "through2": "^2.0.3", "verb-generate-readme": "^0.6.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/node-base/base", "keywords": ["base", "boilerplate", "cache", "del", "get", "inherit", "methods", "set", "starter", "unset", "visit"], "license": "MIT", "main": "index.js", "maintainers": [{"name": "<PERSON>", "url": "https://github.com/doowb"}, {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}], "name": "base", "repository": {"type": "git", "url": "git+https://github.com/node-base/base.git"}, "scripts": {"test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "helpers": ["helper-coverage"], "related": {"description": "There are a number of different plugins available for extending base. Let us know if you create your own!", "hightlight": "generate", "list": ["base-cwd", "base-data", "base-fs", "base-generators", "base-option", "base-pipeline", "base-pkg", "base-plugins", "base-questions", "base-store", "base-task"]}, "reflinks": ["assemble", "boilerplate", "cache-base", "class-utils", "generate", "scaffold", "static-extend", "verb"], "lint": {"reflinks": true}}, "version": "0.11.2"}