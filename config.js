
// 全局的配置文件
module.exports = {
    /**
     * 微信小程序appId
     */
    appId: 'wx6b518ef03e9ea2d7',
    /**
     * 微信小程序appSecret
     */
    appSecret: '1aba35bf5f96a7ec5f8607de8880ad9a',
    /**
     * 设置token加密和解密用到的密钥
     */
    jwtSecretKey: 'clh ^_^',
    /**
     * 设置token的有效期
     */
    expiresIn: '24h',    
    /**
     * 小程序上传图片到本地路径
     */
    uploadPath: 'uploads/',
    /**
     * 本地图片备份压缩后路径
     */
    compressPath: 'backup/',
    /**
     * 从http服务器获取图片的路径
     */
    httpImgPath: '/images/',
    /**
     * ftp 服务器相关配置
     * allowedFtp:是否允许FTP上传   
     * ftpFilePath:上传路径
     * backupPath:备份路径
     */
    allowedFtp: true,
    ftpFilePath: '/home/<USER>/',
    backupPath: '../backup/',

    option: {
        host: '***********',
        port: 21,
        user: 'ftpUser',
        password: 'Fd@269o534',
        keepalive: 10000
    },

    /**
     * 业务类型配置
     * 用于图片管理、数据验证等场景
     */
    businessTypes: ['customer', 'user', 'product', 'follow', 'component', 'order']
}