const express = require('express');
const router = express.Router();
const todo_handler = require('../router_handler/todo');
const expressJoi = require('@escook/express-joi');
const { 
  todo_list_schema, 
  todo_detail_schema, 
  todo_add_record_schema, 
  todo_update_status_schema 
} = require('../schema/todo');

// 获取待办事项列表
router.get('/list', expressJoi(todo_list_schema), todo_handler.getTodoList);

// 获取订单流程节点跟进详情
router.get('/detail', expressJoi(todo_detail_schema), todo_handler.getTodoDetail);

// 添加跟进记录
router.post('/addRecord', expressJoi(todo_add_record_schema), todo_handler.addTodoRecord);

// 更新跟进状态
router.post('/updateStatus', expressJoi(todo_update_status_schema), todo_handler.updateTodoStatus);

module.exports = router;
