const joi = require('joi');
const config = require('../config');

// 图片上传的验证规则
const image_upload_schema = {
  body: {
    businessType: joi.string().optional(),
    businessId: joi.string().optional(),
    description: joi.string().allow('').optional()
  }
};

// 通用图片上传的验证规则
const general_image_upload_schema = {
  body: {
    businessType: joi.string().valid(...config.businessTypes).required(),
    businessId: joi.string().required(),
    operator: joi.string().required()
  }
};

// 获取图片详情的验证规则
const image_detail_schema = {
  params: {
    imageId: joi.string().required()
  }
};

// 获取图片列表的验证规则
const image_list_schema = {
  query: {   
    businessType: joi.string().valid(...config.businessTypes).required(),
    businessId: joi.string().optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 删除图片的验证规则
const image_delete_schema = {
  body: {
    imageId: joi.string().required()
  }
};

// 批量图片上传的验证规则
const multiple_image_upload_schema = {
  body: {
    businessType: joi.string().valid(...config.businessTypes).required(),
    businessId: joi.string().required(),
    operator: joi.string().required()
  }
};

// 通过URL删除图片的验证规则
const image_delete_by_url_schema = {
  query: {
    businessType: joi.string().valid(...config.businessTypes).required(),
    businessId: joi.string().required(),
    operator: joi.string().required(),
    imageUrl: joi.string().required()
  }
};

// 获取业务图片的验证规则
const business_images_schema = {
  query: {
    businessType: joi.string().valid(...config.businessTypes).required(),
    businessId: joi.string().required()
  }
};

module.exports = {
  image_upload_schema,
  general_image_upload_schema,
  image_detail_schema,
  image_list_schema,
  image_delete_schema,
  multiple_image_upload_schema,
  image_delete_by_url_schema,
  business_images_schema 
};