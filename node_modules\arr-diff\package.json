{"_from": "arr-diff@^2.0.0", "_id": "arr-diff@2.0.0", "_inBundle": false, "_integrity": "sha512-dtXTVMkh6VkEEA7OhXnN1Ecb8aAGFdZ1LFxtOCoqj4qkyOJMt7+qs6Ahdy6p/NQCPYsRSXXivhSB/J5E9jmYKA==", "_location": "/arr-diff", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "arr-diff@^2.0.0", "name": "arr-diff", "escapedName": "arr-diff", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/micromatch"], "_resolved": "https://registry.npmjs.org/arr-diff/-/arr-diff-2.0.0.tgz", "_shasum": "8f3b827f955a8bd669697e4a4256ac3ceae356cf", "_spec": "arr-diff@^2.0.0", "_where": "D:\\Code\\node-express\\node_modules\\micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/arr-diff/issues"}, "bundleDependencies": false, "dependencies": {"arr-flatten": "^1.0.1"}, "deprecated": false, "description": "Returns an array with only the unique values from the first array, by excluding all values from additional arrays using strict equality for comparisons.", "devDependencies": {"array-differ": "^1.0.0", "array-slice": "^0.2.3", "benchmarked": "^0.1.4", "chalk": "^1.1.1", "mocha": "*", "should": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/arr-diff", "keywords": ["arr", "array", "diff", "differ", "difference"], "license": "MIT", "main": "index.js", "name": "arr-diff", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/arr-diff.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["arr-flatten", "array-filter", "array-intersection"]}}, "version": "2.0.0"}