const express = require('express');
const router = express.Router();
const permission_handler = require('../router_handler/permission');
const expressJoi = require('@escook/express-joi');
const { 
  permission_list_schema, 
  permission_detail_schema, 
  permission_add_schema, 
  permission_update_schema, 
  permission_delete_schema
} = require('../schema/permission');

// 获取权限模板列表
router.get('/list', expressJoi(permission_list_schema), permission_handler.getPermissionList);

// 获取权限模板详情
router.get('/detail', expressJoi(permission_detail_schema), permission_handler.getPermissionDetail);

// 新增权限模板
router.post('/add', expressJoi(permission_add_schema), permission_handler.addPermission);

// 更新权限模板
router.post('/update', expressJoi(permission_update_schema), permission_handler.updatePermission);

// 删除权限模板
router.post('/delete', expressJoi(permission_delete_schema), permission_handler.deletePermission);

// Android应用专用：获取职位下拉框数据
router.get('/', permission_handler.getPermissions);

module.exports = router;
