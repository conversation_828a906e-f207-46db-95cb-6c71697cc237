{"_from": "dom-serializer@^2.0.0", "_id": "dom-serializer@2.0.0", "_inBundle": false, "_integrity": "sha512-wIkAryiqt/nV5EQKqQpo3SToSOV9J0DnbJqwK7Wv/Trc92zIAYZ4FlMu+JPFW1DfGFt81ZTCGgDEabffXeLyJg==", "_location": "/dom-serializer", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dom-serializer@^2.0.0", "name": "dom-serializer", "escapedName": "dom-serializer", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/cheerio", "/domutils"], "_resolved": "https://registry.npmmirror.com/dom-serializer/-/dom-serializer-2.0.0.tgz", "_shasum": "e41b802e1eedf9f6cae183ce5e622d789d7d8e53", "_spec": "dom-serializer@^2.0.0", "_where": "D:\\Code\\node-express\\node_modules\\cheerio", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/cheeriojs/dom-serializer/issues"}, "bundleDependencies": false, "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}, "deprecated": false, "description": "render domhandler DOM nodes to a string", "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.18.0", "@typescript-eslint/parser": "^5.18.0", "cheerio": "^1.0.0-rc.9", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^7.2.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib/**/*"], "funding": "https://github.com/cheeriojs/dom-serializer?sponsor=1", "homepage": "https://github.com/cheeriojs/dom-serializer#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "keywords": ["html", "xml", "render"], "license": "MIT", "main": "lib/index.js", "module": "lib/esm/index.js", "name": "dom-serializer", "repository": {"type": "git", "url": "git://github.com/cheeriojs/dom-serializer.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint --ignore-path .gitignore .", "lint:prettier": "npm run prettier -- --check", "prepare": "npm run build", "prettier": "prettier \"**/*.{ts,md,json,yml}\" --ignore-path .gitignore", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "2.0.0"}