const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');

// 尝试连接数据库，如果失败则使用文件系统
let db = null;
try {
  db = require('../db/index');
} catch (error) {
  console.log('数据库连接失败，将使用文件系统存储图片信息:', error.message);
}

// 图片类型配置
const IMAGE_TYPES = {
  AVATAR: 'avatar',
  PRODUCT: 'product',
  FOLLOW: 'follow',
  COMPONENT:'component',
  CUSTOMER: 'customer'
};

// 图片存储路径配置
const IMAGE_PATHS = {
  [IMAGE_TYPES.AVATAR]: 'images/avatar',
  [IMAGE_TYPES.PRODUCT]: 'images/product',
  [IMAGE_TYPES.FOLLOW]: 'images/follow',
  [IMAGE_TYPES.COMPONENT]:'images/component',
  [IMAGE_TYPES.CUSTOMER]: 'images/customer'
};

// 创建存储目录
function ensureDirectoryExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// 初始化所有图片目录
function initImageDirectories() {
  Object.values(IMAGE_PATHS).forEach(dirPath => {
    ensureDirectoryExists(dirPath);
  });
}

// 创建动态存储配置
function createStorage(imageType) {
  return multer.diskStorage({
    destination: function (req, file, cb) {
      const uploadPath = IMAGE_PATHS[imageType];
      ensureDirectoryExists(uploadPath);
      cb(null, uploadPath);
    },
    filename: function (req, file, cb) {
      const timestamp = Date.now();
      const randomNum = Math.round(Math.random() * 1E9);
      const extname = path.extname(file.originalname);
      const filename = `${timestamp}-${randomNum}${extname}`;
      cb(null, filename);
    }
  });
}

// 创建multer实例
function createUploader(imageType, maxFiles = 10) {
  const storage = createStorage(imageType);

  return multer({
    storage: storage,
    fileFilter: function (req, file, cb) {
      if (file.mimetype.startsWith('image/')) {
        cb(null, true);
      } else {
        cb(new Error('只允许上传图片文件'), false);
      }
    },
    limits: {
      fileSize: 10 * 1024 * 1024 // 限制文件大小为10MB
    }
  });
}

// 保存图片信息到数据库
async function saveImageToDatabase(imageInfo) {
  const {
    id,
    originalName,
    fileName,
    filePath,
    fileSize,
    mimeType,
    imageType,
    businessType = null,
    businessId = null,
    uploadUserId = null,
    uploadUserName = null
  } = imageInfo;

  const sql = `
    INSERT INTO images (
      id, original_name, file_name, file_path, file_size,
      mime_type, image_type, business_type, business_id,
      upload_user_id, upload_user_name, status
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'active')
  `;

  const params = [
    id, originalName, fileName, filePath, fileSize,
    mimeType, imageType, businessType, businessId,
    uploadUserId, uploadUserName
  ];

  return new Promise((resolve, reject) => {
    db.query(sql, params, (err, result) => {
      if (err) {
        // 如果数据库操作失败，回退到文件系统存储
        console.log('数据库操作失败，回退到文件系统存储:', err.message);

        const imageInfoPath = path.join('images', 'image_info.json');

        let imageInfoList = [];
        if (fs.existsSync(imageInfoPath)) {
          try {
            const data = fs.readFileSync(imageInfoPath, 'utf8');
            imageInfoList = JSON.parse(data);
          } catch (error) {
            console.error('读取图片信息文件失败:', error);
          }
        }

        imageInfoList.push({
          ...imageInfo,
          create_time: new Date().toISOString(),
          status: 'active'
        });

        try {
          fs.writeFileSync(imageInfoPath, JSON.stringify(imageInfoList, null, 2));
          resolve({ insertId: id });
        } catch (error) {
          reject(error);
        }
      } else {
        resolve(result);
      }
    });
  });
}

// 根据ID获取图片信息
async function getImageById(imageId) {
  // 如果没有数据库连接，从文件系统读取
  if (!db) {
    const imageInfoPath = path.join('image', 'image_info.json');

    if (!fs.existsSync(imageInfoPath)) {
      return Promise.resolve(null);
    }

    try {
      const data = fs.readFileSync(imageInfoPath, 'utf8');
      const imageInfoList = JSON.parse(data);
      const imageInfo = imageInfoList.find(img => img.id === imageId && img.status === 'active');
      return Promise.resolve(imageInfo || null);
    } catch (error) {
      return Promise.reject(error);
    }
  }

  const sql = 'SELECT * FROM images WHERE id = ? AND status = "active"';

  return new Promise((resolve, reject) => {
    db.query(sql, [imageId], (err, results) => {
      if (err) {
        reject(err);
      } else {
        resolve(results[0] || null);
      }
    });
  });
}

// 根据业务信息获取图片列表
async function getImagesByBusiness(businessType, businessId) {
  const sql = `
    SELECT * FROM images
    WHERE business_type = ? AND business_id = ? AND status = "active"
    ORDER BY create_time DESC
  `;

  return new Promise((resolve, reject) => {
    db.query(sql, [businessType, businessId], (err, results) => {
      if (err) {
        reject(err);
      } else {
        resolve(results);
      }
    });
  });
}

// 删除图片（软删除）
async function deleteImage(imageId) {
  // 如果没有数据库连接，从文件系统删除
  if (!db) {
    const imageInfoPath = path.join('image', 'image_info.json');

    if (!fs.existsSync(imageInfoPath)) {
      return Promise.resolve({ affectedRows: 0 });
    }

    try {
      const data = fs.readFileSync(imageInfoPath, 'utf8');
      let imageInfoList = JSON.parse(data);
      const imageIndex = imageInfoList.findIndex(img => img.id === imageId);

      if (imageIndex !== -1) {
        imageInfoList[imageIndex].status = 'deleted';
        fs.writeFileSync(imageInfoPath, JSON.stringify(imageInfoList, null, 2));
        return Promise.resolve({ affectedRows: 1 });
      } else {
        return Promise.resolve({ affectedRows: 0 });
      }
    } catch (error) {
      return Promise.reject(error);
    }
  }

  const sql = 'UPDATE images SET status = "deleted" WHERE id = ?';

  return new Promise((resolve, reject) => {
    db.query(sql, [imageId], (err, result) => {
      if (err) {
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
}

// 处理文件上传并保存到数据库
async function handleFileUpload(files, imageType, options = {}) {
  const {
    businessType = null,
    businessId = null,
    uploadUserId = null,
    uploadUserName = null
  } = options;

  const savedImages = [];

  for (const file of files) {
    const imageId = uuidv4();
    const imageInfo = {
      id: imageId,
      originalName: file.originalname,
      fileName: file.filename,
      filePath: file.path,
      fileSize: file.size,
      mimeType: file.mimetype,
      imageType: imageType,
      businessType: businessType,
      businessId: businessId,
      uploadUserId: uploadUserId,
      uploadUserName: uploadUserName
    };

    try {
      await saveImageToDatabase(imageInfo);
      savedImages.push({
        id: imageId,
        url: `${IMAGE_PATHS[imageType]}/${file.filename}`,
        originalName: file.originalname,
        fileName: file.filename,
        fileSize: file.size
      });
    } catch (error) {
      console.error('保存图片信息到数据库失败:', error);
      // 删除已上传的文件
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path);
      }
      throw error;
    }
  }

  return savedImages;
}

// 初始化图片目录
initImageDirectories();

module.exports = {
  IMAGE_TYPES,
  IMAGE_PATHS,
  createUploader,
  saveImageToDatabase,
  getImageById,
  getImagesByBusiness,
  deleteImage,
  handleFileUpload,  
  ensureDirectoryExists
};
