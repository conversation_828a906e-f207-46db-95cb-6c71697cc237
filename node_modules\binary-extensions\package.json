{"_from": "binary-extensions@^2.0.0", "_id": "binary-extensions@2.3.0", "_inBundle": false, "_integrity": "sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==", "_location": "/binary-extensions", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "binary-extensions@^2.0.0", "name": "binary-extensions", "escapedName": "binary-extensions", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/is-binary-path"], "_resolved": "https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz", "_shasum": "f6e14a97858d327252200242d4ccfe522c445522", "_spec": "binary-extensions@^2.0.0", "_where": "D:\\Code\\node-express\\node_modules\\is-binary-path", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "bugs": {"url": "https://github.com/sindresorhus/binary-extensions/issues"}, "bundleDependencies": false, "deprecated": false, "description": "List of binary file extensions", "devDependencies": {"ava": "^1.4.1", "tsd": "^0.7.2", "xo": "^0.24.0"}, "engines": {"node": ">=8"}, "files": ["index.js", "index.d.ts", "binary-extensions.json", "binary-extensions.json.d.ts"], "funding": "https://github.com/sponsors/sindresorhus", "homepage": "https://github.com/sindresorhus/binary-extensions#readme", "keywords": ["binary", "extensions", "extension", "file", "json", "list", "array"], "license": "MIT", "name": "binary-extensions", "repository": {"type": "git", "url": "git+https://github.com/sindresorhus/binary-extensions.git"}, "scripts": {"test": "xo && ava && tsd"}, "sideEffects": false, "version": "2.3.0"}