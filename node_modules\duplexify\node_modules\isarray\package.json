{"_from": "isarray@~1.0.0", "_id": "isarray@1.0.0", "_inBundle": false, "_integrity": "sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==", "_location": "/duplexify/isarray", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "isarray@~1.0.0", "name": "isarray", "escapedName": "isarray", "rawSpec": "~1.0.0", "saveSpec": null, "fetchSpec": "~1.0.0"}, "_requiredBy": ["/duplexify/readable-stream"], "_resolved": "https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz", "_shasum": "bb935d48582cba168c06834957a54a3e07124f11", "_spec": "isarray@~1.0.0", "_where": "D:\\Code\\node-express\\node_modules\\duplexify\\node_modules\\readable-stream", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "bugs": {"url": "https://github.com/juliangruber/isarray/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Array#isArray for older browsers", "devDependencies": {"tape": "~2.13.4"}, "homepage": "https://github.com/juliangruber/isarray", "keywords": ["browser", "isarray", "array"], "license": "MIT", "main": "index.js", "name": "isarray", "repository": {"type": "git", "url": "git://github.com/juliangruber/isarray.git"}, "scripts": {"test": "tape test.js"}, "testling": {"files": "test.js", "browsers": ["ie/8..latest", "firefox/17..latest", "firefox/nightly", "chrome/22..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.0.0"}