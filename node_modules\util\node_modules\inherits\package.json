{"_from": "inherits@2.0.3", "_id": "inherits@2.0.3", "_inBundle": false, "_integrity": "sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==", "_location": "/util/inherits", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "inherits@2.0.3", "name": "inherits", "escapedName": "inherits", "rawSpec": "2.0.3", "saveSpec": null, "fetchSpec": "2.0.3"}, "_requiredBy": ["/util"], "_resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz", "_shasum": "633c2c83e3da42a502f52466022480f4208261de", "_spec": "inherits@2.0.3", "_where": "D:\\Code\\node-express\\node_modules\\util", "browser": "./inherits_browser.js", "bugs": {"url": "https://github.com/isaacs/inherits/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Browser-friendly inheritance fully compatible with standard node.js inherits()", "devDependencies": {"tap": "^7.1.0"}, "files": ["inherits.js", "inherits_browser.js"], "homepage": "https://github.com/isaacs/inherits#readme", "keywords": ["inheritance", "class", "klass", "oop", "object-oriented", "inherits", "browser", "browserify"], "license": "ISC", "main": "./inherits.js", "name": "inherits", "repository": {"type": "git", "url": "git://github.com/isaacs/inherits.git"}, "scripts": {"test": "node test"}, "version": "2.0.3"}