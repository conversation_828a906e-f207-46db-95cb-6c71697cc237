{"_from": "bcryptjs@^2.4.3", "_id": "bcryptjs@2.4.3", "_inBundle": false, "_integrity": "sha512-V/Hy/X9Vt7f3BbPJEi8BdVFMByHi+jNXrYkW3huaybV/kQ0KJg0Y6PkEMbn+zeT+i+SiKZ/HMqJGIIt4LZDqNQ==", "_location": "/bcryptjs", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "bcryptjs@^2.4.3", "name": "bcryptjs", "escapedName": "bcryptjs", "rawSpec": "^2.4.3", "saveSpec": null, "fetchSpec": "^2.4.3"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/bcryptjs/-/bcryptjs-2.4.3.tgz", "_shasum": "9ab5627b93e60621ff7cdac5da9733027df1d0cb", "_spec": "bcryptjs@^2.4.3", "_where": "D:\\Code\\node-express", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "dist/bcrypt.js", "bugs": {"url": "https://github.com/dcodeIO/bcrypt.js/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "shaneG<PERSON><EMAIL>", "url": "https://github.com/shaneGirish"}, {"name": "<PERSON>", "url": "https://github.com/alexmurray"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/geekymole"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/nisa<PERSON>son"}], "dependencies": {}, "deprecated": false, "description": "Optimized bcrypt in plain JavaScript with zero dependencies. Compatible to 'bcrypt'.", "devDependencies": {"bcrypt": "latest", "closurecompiler": "~1", "metascript": "~0.18", "testjs": "~1", "utfx": "~1"}, "homepage": "https://github.com/dcodeIO/bcrypt.js#readme", "keywords": ["bcrypt", "password", "auth", "authentication", "encryption", "crypt", "crypto"], "license": "MIT", "main": "index.js", "name": "bcryptjs", "repository": {"type": "url", "url": "git+https://github.com/dcodeIO/bcrypt.js.git"}, "scripts": {"build": "node scripts/build.js", "compile": "node node_modules/closurecompiler/bin/ccjs dist/bcrypt.js --compilation_level=SIMPLE_OPTIMIZATIONS --create_source_map=dist/bcrypt.min.map > dist/bcrypt.min.js", "compress": "gzip -c -9 dist/bcrypt.min.js > dist/bcrypt.min.js.gz", "make": "npm run build && npm run compile && npm run compress && npm test", "test": "node node_modules/testjs/bin/testjs"}, "version": "2.4.3"}