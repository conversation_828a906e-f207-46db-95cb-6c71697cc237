const joi = require('joi');

// 获取流程节点列表的验证规则
const process_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取流程节点详情的验证规则
const process_detail_schema = {
  query: {
    id: joi.number().integer().required()
  }
};

// 新增流程节点的验证规则
const process_add_schema = {
  body: {
    name: joi.string().required(),
    code: joi.string().required(),
    sequence: joi.number().integer().min(1).required(),
    status: joi.string().valid('1', '0').default('1'),
    description: joi.string().allow('').optional()
  }
};

// 更新流程节点的验证规则
const process_update_schema = {
  body: {
    id: joi.number().integer().required(),
    name: joi.string().required(),
    code: joi.string().required(),
    sequence: joi.number().integer().min(1).required(),
    status: joi.string().valid('1', '0').default('1'),
    description: joi.string().allow('').optional()
  }
};

// 删除流程节点的验证规则
const process_delete_schema = {
  body: {
    id: joi.number().integer().required()
  }
};

module.exports = {
  process_list_schema,
  process_detail_schema,
  process_add_schema,
  process_update_schema,
  process_delete_schema
};
