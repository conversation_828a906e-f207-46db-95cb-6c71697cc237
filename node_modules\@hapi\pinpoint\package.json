{"_from": "@hapi/pinpoint@^2.0.0", "_id": "@hapi/pinpoint@2.0.1", "_inBundle": false, "_integrity": "sha512-EKQmr16tM8s16vTT3cA5L0kZZcTMU5DUOZTuvpnY738m+jyP3JIUj+Mm1xc1rsLkGBQ/gVnfKYPwOmPg1tUR4Q==", "_location": "/@hapi/pinpoint", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@hapi/pinpoint@^2.0.0", "name": "@hapi/pinpoint", "escapedName": "@hapi%2fpinpoint", "scope": "@hapi", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/@hapi/joi"], "_resolved": "https://registry.npmjs.org/@hapi/pinpoint/-/pinpoint-2.0.1.tgz", "_shasum": "32077e715655fc00ab8df74b6b416114287d6513", "_spec": "@hapi/pinpoint@^2.0.0", "_where": "D:\\Code\\node-express\\node_modules\\@hapi\\joi", "bugs": {"url": "https://github.com/hapijs/pinpoint/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Return the filename and line number of the calling function", "devDependencies": {"@hapi/code": "^9.0.3", "@hapi/lab": "^25.1.2", "@types/node": "^14.18.36", "typescript": "4.0.x"}, "files": ["lib"], "homepage": "https://github.com/hapijs/pinpoint#readme", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@hapi/pinpoint", "repository": {"type": "git", "url": "git://github.com/hapijs/pinpoint.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "types": "lib/index.d.ts", "version": "2.0.1"}