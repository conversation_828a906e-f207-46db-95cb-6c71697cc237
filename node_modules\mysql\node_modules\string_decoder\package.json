{"_from": "string_decoder@~1.1.1", "_id": "string_decoder@1.1.1", "_inBundle": false, "_integrity": "sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==", "_location": "/mysql/string_decoder", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "string_decoder@~1.1.1", "name": "string_decoder", "escapedName": "string_decoder", "rawSpec": "~1.1.1", "saveSpec": null, "fetchSpec": "~1.1.1"}, "_requiredBy": ["/mysql/readable-stream"], "_resolved": "https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz", "_shasum": "9cf1611ba62685d7030ae9e4ba34149c3af03fc8", "_spec": "string_decoder@~1.1.1", "_where": "D:\\Code\\node-express\\node_modules\\mysql\\node_modules\\readable-stream", "bugs": {"url": "https://github.com/nodejs/string_decoder/issues"}, "bundleDependencies": false, "dependencies": {"safe-buffer": "~5.1.0"}, "deprecated": false, "description": "The string_decoder module from Node core", "devDependencies": {"babel-polyfill": "^6.23.0", "core-util-is": "^1.0.2", "inherits": "^2.0.3", "tap": "~0.4.8"}, "homepage": "https://github.com/nodejs/string_decoder", "keywords": ["string", "decoder", "browser", "browserify"], "license": "MIT", "main": "lib/string_decoder.js", "name": "string_decoder", "repository": {"type": "git", "url": "git://github.com/nodejs/string_decoder.git"}, "scripts": {"ci": "tap test/parallel/*.js test/ours/*.js --tap | tee test.tap && node test/verify-dependencies.js", "test": "tap test/parallel/*.js && node test/verify-dependencies"}, "version": "1.1.1"}