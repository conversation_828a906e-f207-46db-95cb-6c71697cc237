{"_from": "better-assert@~1.0.0", "_id": "better-assert@1.0.2", "_inBundle": false, "_integrity": "sha512-bY<PERSON><PERSON>2DFlpK1XmGs6fvlLRUN29QISM3GBuUwSFsMY2XRx4AvC0WNCS57j4c/xGrK2RS24C1w3YoBOsw9fT46tQ==", "_location": "/better-assert", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "better-assert@~1.0.0", "name": "better-assert", "escapedName": "better-assert", "rawSpec": "~1.0.0", "saveSpec": null, "fetchSpec": "~1.0.0"}, "_requiredBy": ["/parsejson", "/parseqs", "/parseuri"], "_resolved": "https://registry.npmjs.org/better-assert/-/better-assert-1.0.2.tgz", "_shasum": "40866b9e1b9e0b55b481894311e68faffaebc522", "_spec": "better-assert@~1.0.0", "_where": "D:\\Code\\node-express\\node_modules\\parsejson", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/visionmedia/better-assert/issues"}, "bundleDependencies": false, "contributors": [{"name": "TonyH<PERSON>", "email": "<EMAIL>"}, {"name": "ForbesLindesay"}], "dependencies": {"callsite": "1.0.0"}, "deprecated": false, "description": "Better assertions for node, reporting the expr, filename, lineno etc", "engines": {"node": "*"}, "homepage": "https://github.com/visionmedia/better-assert#readme", "keywords": ["assert", "stack", "trace", "debug"], "main": "index", "name": "better-assert", "repository": {"type": "git", "url": "git+https://github.com/visionmedia/better-assert.git"}, "version": "1.0.2"}