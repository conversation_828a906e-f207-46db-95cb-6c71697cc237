{"_from": "cookie@0.3.1", "_id": "cookie@0.3.1", "_inBundle": false, "_integrity": "sha512-+IJOX0OqlHCszo2mBUq+SrEbCj6w7Kpffqx60zYbPTFaO4+yYgRjHwcZNpWvaTylDHaV7PPmBHzSecZiMhtPgw==", "_location": "/cookie", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "cookie@0.3.1", "name": "cookie", "escapedName": "cookie", "rawSpec": "0.3.1", "saveSpec": null, "fetchSpec": "0.3.1"}, "_requiredBy": ["/engine.io"], "_resolved": "https://registry.npmjs.org/cookie/-/cookie-0.3.1.tgz", "_shasum": "e7e0a1f9ef43b4c8ba925c5c5a96e806d16873bb", "_spec": "cookie@0.3.1", "_where": "D:\\Code\\node-express\\node_modules\\engine.io", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jshttp/cookie/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "deprecated": false, "description": "HTTP server cookie parsing and serialization", "devDependencies": {"istanbul": "0.4.3", "mocha": "1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "homepage": "https://github.com/jshttp/cookie#readme", "keywords": ["cookie", "cookies"], "license": "MIT", "name": "cookie", "repository": {"type": "git", "url": "git+https://github.com/jshttp/cookie.git"}, "scripts": {"test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "0.3.1"}