const db = require('../db/index')
const log = require('./log')
// 获取部门列表
const getDepartmentList = (req, res) => {
  try {
    const user = req.user;
    const dataSql = "SELECT * FROM departments WHERE `status` = 1 ";
    db.query(dataSql, null, (err, results) => {
      if (err) return res.output(err)
     
      // 返回结果
      res.output(null, 200, results, true);
    });
    // 记录日志
    log.add("sysLog", "查询部门列表", user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取部门详情
const getDepartmentDetail = (req, res) => {
  try {
    const { id } = req.query;
    const sql = 'select * from departments where id= ?'
    db.query(sql, id, (err, results) => {
      if (err) return res.output(err)
      if (results.length !== 1) return res.output(new Error("获取部门信息失败！"), 400)
      res.output(null, 200, results[0], true);
    })
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 新增部门
const addDepartment = (req, res) => {
  if (!user || !user.username) {
    return res.output(new Error("未登录或登录已过期"), 401);
  }
  try {
    const { name, code, parentCode } = req.body;
    const sqlStr = "insert into departments set ?";
    db.query(sqlStr, { name, code, parentCode }, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("添加部门失败！"), 400);
      res.output(null, 200, { id: results.insertId,message: "添加部门成功" }, true);
    });
    log.add("sysLog", "添加部门:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 更新部门
const updateDepartment = (req, res) => {
  // 从 JWT token 中获取用户信息
  const user = req.user;
  if (!user || !user.username) {
    return res.output(new Error("未登录或登录已过期"), 401);
  }
  try {
    const { id, name, code, parentCode } = req.body;
    const sqlStr = 'update departments set name=?,code=?,parentCode=? where id =?'
    db.query(sqlStr, [name, code, parentCode, id], (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('更新部门信息失败！'), 400)
      res.output(null, 200, { id: id, name: name, code: code, parentCode: parentCode }, true)
    })
    log.add("sysLog", "修改部门信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 删除部门
const deleteDepartment = (req, res) => {
  // 从 JWT token 中获取用户信息
  const user = req.user;
  if (!user || !user.username) {
    return res.output(new Error("未登录或登录已过期"), 401);
  }
  try {
    const { id } = req.body;
    const sqlStr = 'update departments set status = 0 where id =?'
    db.query(sqlStr, id, (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('删除部门信息失败！'), 400)
      res.output(null, 200, { id: id }, true)
    })
    log.add("sysLog", "删除部门信息:" + id, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// Android应用专用：获取部门下拉框数据
const getDepartments = (req, res) => {
  try {
    // 查询数据
    const dataSql = "SELECT * FROM departments WHERE status=1 ";
    db.query(dataSql, null, (err, results) => {
      if (err) {
        console.error("查询数据出错:", err);
        return res.output(err, 500);
      }
      // 返回结果
      res.output(null, 200, results, true);
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

module.exports = {
  getDepartmentList,
  getDepartmentDetail,
  addDepartment,
  updateDepartment,
  deleteDepartment,
  getDepartments
};
