const express = require('express');
const router = express.Router();
const job_handler = require('../router_handler/job');
const expressJoi = require('@escook/express-joi');
const {
  job_list_schema,
  job_detail_schema,
  job_add_schema,
  job_update_schema,
  job_delete_schema
} = require('../schema/job');

// 获取岗位列表
router.get('/list', expressJoi(job_list_schema), job_handler.getJobList);

// 获取岗位详情
router.get('/detail', expressJoi(job_detail_schema), job_handler.getJobDetail);

// 新增岗位
router.post('/add', expressJoi(job_add_schema), job_handler.addJob);

// 更新岗位
router.post('/update', expressJoi(job_update_schema), job_handler.updateJob);

// 删除岗位(伪删除)
router.post('/delete', expressJoi(job_delete_schema), job_handler.deleteJob);

// Android应用专用：获取岗位下拉框数据
router.get('/', job_handler.getPosts);

module.exports = router;
