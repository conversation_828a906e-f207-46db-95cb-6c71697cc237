{"_from": "accepts@1.3.3", "_id": "accepts@1.3.3", "_inBundle": false, "_integrity": "sha512-AOPopplFOUlmUugwiZUCDpOwmqvSgdCyE8iJVLWI4NcB7qfMKQN34dn5xYtlUU03XGG5egRWW4NW5gIxpa5hEA==", "_location": "/accepts", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "accepts@1.3.3", "name": "accepts", "escapedName": "accepts", "rawSpec": "1.3.3", "saveSpec": null, "fetchSpec": "1.3.3"}, "_requiredBy": ["/engine.io"], "_resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.3.tgz", "_shasum": "c3ca7434938648c3e0d9c1e328dd68b622c284ca", "_spec": "accepts@1.3.3", "_where": "D:\\Code\\node-express\\node_modules\\engine.io", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"mime-types": "~2.1.11", "negotiator": "0.6.1"}, "deprecated": false, "description": "Higher-level content negotiation", "devDependencies": {"istanbul": "0.4.3", "mocha": "~1.21.5"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/jshttp/accepts#readme", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "name": "accepts", "repository": {"type": "git", "url": "git+https://github.com/jshttp/accepts.git"}, "scripts": {"test": "mocha --reporter spec --check-leaks --bail test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/"}, "version": "1.3.3"}