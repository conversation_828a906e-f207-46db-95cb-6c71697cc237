{"_from": "@hapi/hoek@^9.3.0", "_id": "@hapi/hoek@9.3.0", "_inBundle": false, "_integrity": "sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ==", "_location": "/@hapi/hoek", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@hapi/hoek@^9.3.0", "name": "@hapi/hoek", "escapedName": "@hapi%2fhoek", "scope": "@hapi", "rawSpec": "^9.3.0", "saveSpec": null, "fetchSpec": "^9.3.0"}, "_requiredBy": ["/@hapi/address", "/@hapi/joi", "/@hapi/topo", "/@sideway/address", "/joi"], "_resolved": "https://registry.npmjs.org/@hapi/hoek/-/hoek-9.3.0.tgz", "_shasum": "8368869dcb735be2e7f5cb7647de78e167a251fb", "_spec": "@hapi/hoek@^9.3.0", "_where": "D:\\Code\\node-express\\node_modules\\joi", "bugs": {"url": "https://github.com/hapijs/hoek/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "General purpose node utilities", "devDependencies": {"@hapi/code": "8.x.x", "@hapi/eslint-plugin": "*", "@hapi/lab": "^24.0.0", "typescript": "~4.0.2"}, "eslintConfig": {"extends": ["plugin:@hapi/module"]}, "files": ["lib"], "homepage": "https://github.com/hapijs/hoek#readme", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@hapi/hoek", "repository": {"type": "git", "url": "git://github.com/hapijs/hoek.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "types": "lib/index.d.ts", "version": "9.3.0"}