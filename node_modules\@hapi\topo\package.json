{"_from": "@hapi/topo@^5.1.0", "_id": "@hapi/topo@5.1.0", "_inBundle": false, "_integrity": "sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg==", "_location": "/@hapi/topo", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@hapi/topo@^5.1.0", "name": "@hapi/topo", "escapedName": "@hapi%2ftopo", "scope": "@hapi", "rawSpec": "^5.1.0", "saveSpec": null, "fetchSpec": "^5.1.0"}, "_requiredBy": ["/@hapi/joi", "/joi"], "_resolved": "https://registry.npmjs.org/@hapi/topo/-/topo-5.1.0.tgz", "_shasum": "dc448e332c6c6e37a4dc02fd84ba8d44b9afb012", "_spec": "@hapi/topo@^5.1.0", "_where": "D:\\Code\\node-express\\node_modules\\joi", "bugs": {"url": "https://github.com/hapijs/topo/issues"}, "bundleDependencies": false, "dependencies": {"@hapi/hoek": "^9.0.0"}, "deprecated": false, "description": "Topological sorting with grouping support", "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "24.x.x", "typescript": "~4.0.2"}, "files": ["lib"], "homepage": "https://github.com/hapijs/topo#readme", "keywords": ["topological", "sort", "toposort", "topsort"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@hapi/topo", "repository": {"type": "git", "url": "git://github.com/hapijs/topo.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "types": "lib/index.d.ts", "version": "5.1.0"}