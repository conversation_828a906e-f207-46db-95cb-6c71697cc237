{"_from": "side-channel-list@^1.0.0", "_id": "side-channel-list@1.0.0", "_inBundle": false, "_integrity": "sha512-FCLHtRD/gnpCiCHEiJLOwdmFP+wzCmDEkc9y7NsYxeF4u7Btsn1ZuwgwJGxImImHicJArLP4R0yX4c2KCrMrTA==", "_location": "/side-channel-list", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "side-channel-list@^1.0.0", "name": "side-channel-list", "escapedName": "side-channel-list", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/side-channel"], "_resolved": "https://registry.npmjs.org/side-channel-list/-/side-channel-list-1.0.0.tgz", "_shasum": "10cb5984263115d3b7a0e336591e290a830af8ad", "_spec": "side-channel-list@^1.0.0", "_where": "D:\\Code\\node-express\\node_modules\\side-channel", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/side-channel-list/issues"}, "bundleDependencies": false, "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "deprecated": false, "description": "Store information about any JS value in a side channel, using a linked list", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "eclint": "^2.8.1", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./package.json": "./package.json"}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "homepage": "https://github.com/ljharb/side-channel-list#readme", "keywords": [], "license": "MIT", "main": "index.js", "name": "side-channel-list", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/side-channel-list.git"}, "scripts": {"lint": "eslint --ext=js,mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md && eclint check $(git ls-files | xargs find 2> /dev/null | grep -vE 'node_modules|\\.git')", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "types": "./index.d.ts", "version": "1.0.0"}