{"_from": "bignumber.js@9.0.0", "_id": "bignumber.js@9.0.0", "_inBundle": false, "_integrity": "sha512-t/OYhhJ2SD+YGBQcjY8GzzDHEk9f3nerxjtfa6tlMXfe7frs/WozhvCNoGvpM0P3bNf3Gq5ZRMlGr5f3r4/N8A==", "_location": "/bignumber.js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "bignumber.js@9.0.0", "name": "bignumber.js", "escapedName": "bignumber.js", "rawSpec": "9.0.0", "saveSpec": null, "fetchSpec": "9.0.0"}, "_requiredBy": ["/mysql"], "_resolved": "https://registry.npmjs.org/bignumber.js/-/bignumber.js-9.0.0.tgz", "_shasum": "805880f84a329b5eac6e7cb6f8274b6d82bdf075", "_spec": "bignumber.js@9.0.0", "_where": "D:\\Code\\node-express\\node_modules\\mysql", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": "bignumber.js", "bugs": {"url": "https://github.com/MikeMcl/bignumber.js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "A library for arbitrary-precision decimal and non-decimal arithmetic", "engines": {"node": "*"}, "homepage": "https://github.com/MikeMcl/bignumber.js#readme", "keywords": ["arbitrary", "precision", "arithmetic", "big", "number", "decimal", "float", "biginteger", "bigdecimal", "bignumber", "bigint", "bignum"], "license": "MIT", "main": "bignumber", "module": "bignumber.mjs", "name": "bignumber.js", "repository": {"type": "git", "url": "git+https://github.com/MikeMcl/bignumber.js.git"}, "scripts": {"build": "uglifyjs bignumber.js --source-map -c -m -o bignumber.min.js", "test": "node test/test"}, "types": "bignumber.d.ts", "version": "9.0.0"}