{"_from": "htmlparser2@^8.0.1", "_id": "htmlparser2@8.0.2", "_inBundle": false, "_integrity": "sha512-GYdjWKDkbRLkZ5geuHs5NY1puJ+PXwP7+fHPRz06Eirsb9ugf6d8kkXav6ADhcODhFFPMIXyxkxSuMf3D6NCFA==", "_location": "/htmlparser2", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "htmlparser2@^8.0.1", "name": "htmlparser2", "escapedName": "htmlparser2", "rawSpec": "^8.0.1", "saveSpec": null, "fetchSpec": "^8.0.1"}, "_requiredBy": ["/cheerio"], "_resolved": "https://registry.npmmirror.com/htmlparser2/-/htmlparser2-8.0.2.tgz", "_shasum": "f002151705b383e62433b5cf466f5b716edaec21", "_spec": "htmlparser2@^8.0.1", "_where": "D:\\Code\\node-express\\node_modules\\cheerio", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/htmlparser2/issues"}, "bundleDependencies": false, "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.0.1", "entities": "^4.4.0"}, "deprecated": false, "description": "Fast & forgiving HTML/XML parser", "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^18.15.5", "@typescript-eslint/eslint-plugin": "^5.56.0", "@typescript-eslint/parser": "^5.56.0", "eslint": "^8.36.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-n": "^15.6.1", "eslint-plugin-unicorn": "^46.0.0", "jest": "^29.5.0", "prettier": "^2.8.6", "ts-jest": "^29.0.5", "typescript": "^4.9.5"}, "directories": {"lib": "lib/"}, "exports": {".": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "./lib/WritableStream": {"require": "./lib/WritableStream.js", "import": "./lib/esm/WritableStream.js"}}, "files": ["lib/**/*"], "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "homepage": "https://github.com/fb55/htmlparser2#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": ["$1", "$1.js"]}}, "keywords": ["html", "parser", "streams", "xml", "dom", "rss", "feed", "atom"], "license": "MIT", "main": "lib/index.js", "module": "lib/esm/index.js", "name": "htmlparser2", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git://github.com/fb55/htmlparser2.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/htmlparser2/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "prepare": "npm run build", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "8.0.2"}