const joi = require('joi');

// 获取职位列表的验证规则
const position_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取职位详情的验证规则
const position_detail_schema = {
  query: {
    id: joi.number().integer().required()
  }
};

// 新增职位的验证规则
const position_add_schema = {
  body: {
    name: joi.string().required(),
    code: joi.string().required(),
    status: joi.string().valid('1', '0').default('1')
  }
};

// 更新职位的验证规则
const position_update_schema = {
  body: {
    id: joi.number().integer().required(),
    name: joi.string().required(),
    code: joi.string().required(),
    status: joi.string().valid('1', '0').default('1')
  }
};

// 删除职位的验证规则
const position_delete_schema = {
  body: {
    id: joi.number().integer().required()
  }
};

module.exports = {
  position_list_schema,
  position_detail_schema,
  position_add_schema,
  position_update_schema,
  position_delete_schema
};
