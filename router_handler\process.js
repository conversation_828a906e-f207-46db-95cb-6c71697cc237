const db = require('../db/index')
const log = require('./log')
// 获取流程节点列表
const getProcessList = (req, res) => {
  try {
    const user = req.user;
    const { keyword = '', page = 1, size = 10 } = req.query;
    // 构建SQL查询
    let extSql = "";
    let params = [];

    if (keyword && keyword.trim() !== '') {
      extSql = "AND (`name` LIKE ? OR 'code' LIKE ?) ";
      const searchTerm = `%${keyword}%`;
      params = [searchTerm, searchTerm, searchTerm];
    }

    // 计算分页
    const offset = (page - 1) * size;

    // 查询总数
    const countSql = "SELECT COUNT(*) as total FROM processes WHERE  `status` = 1 " + extSql;
    db.query(countSql, params, (countErr, countResults) => {
      if (countErr) {
        console.error("查询总数出错:", countErr);
        return res.output(countErr, 500);
      }

      const total = countResults[0].total;

      // 查询数据
      const dataSql = "SELECT * FROM processes WHERE `status` = 1 " + extSql + " LIMIT ?, ?";
      const dataParams = [...params, offset, size];
      db.query(dataSql, dataParams, (err, results) => {
        if (err) {
          console.error("查询数据出错:", err);
          return res.output(err, 500);
        }

        // 返回结果
        res.output(null, 200, {
          total: total,
          page: page,
          size: size,
          list: results
        }, true);
      });
    });
    // 记录日志
    log.add("sysLog", "查询流程节点列表:" + keyword, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取流程节点详情
const getProcessDetail = (req, res) => {
  try {
    const { id } = req.query;
    const sql = 'select * from processes where id= ?'
    db.query(sql, id, (err, results) => {
      if (err) return res.output(err)
      if (results.length !== 1) return res.output(new Error("获取流程节点信息失败！"), 400)
      res.output(null, 200, results[0], true);
    })
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 新增流程节点
const addProcess = (req, res) => {
  try {
    const user = req.user;
    const { name, code, status, sequence, description } = req.body;
    const sqlStr = "insert into processes set ?";
    db.query(sqlStr, { name, code, status, sequence, description }, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("新增流程节点失败！"), 400);
      res.output(null, 200, { id: results.insertId,message: "新增流程节点成功" });
    });
    log.add("sysLog", "新增流程节点信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 更新流程节点
const updateProcess = (req, res) => {
  try {
    const user = req.user;
    const { id, name, code, status, sequence, description } = req.body;
    const sqlStr = 'update processes set name=?,status=?,sequence=?,description=? where id =?'
    db.query(sqlStr, [name, status, sequence, description, id], (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('更新流程节点信息失败！'), 400)
      res.output(null, 200, { id: id, name: name, code: code, status: status }, true)
    })
    log.add("sysLog", "修改流程节点信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 删除流程节点
const deleteProcess = (req, res) => {
  try {
    const user = req.user;
    const { id } = req.body;
    const sqlStr = 'update processes set delFlag = 1 where id =?'
    db.query(sqlStr, id, (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('删除流程节点信息失败！'), 400)
      res.output(null, 200, { id: id }, true)
    })
    log.add("sysLog", "删除流程节点信息:" + id, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// Android应用专用：获取流程节点下拉框数据
const getProcesses = (req, res) => {
  try {
    // 查询数据
    const dataSql = "SELECT * FROM processes WHERE delFlag=0 order by sequence ";
    db.query(dataSql, null, (err, results) => {
      if (err) {
        console.error("查询数据出错:", err);
        return res.output(err, 500);
      }
      // 返回结果
      res.output(null, 200, results, true);
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};


module.exports = {
  getProcessList,
  getProcessDetail,
  addProcess,
  updateProcess,
  deleteProcess,
  getProcesses
};
