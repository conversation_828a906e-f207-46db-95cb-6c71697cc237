const express = require('express');
const router = express.Router();
const product_handler = require('../router_handler/product');
const expressJoi = require('@escook/express-joi');
const { 
  product_list_schema, 
  product_detail_schema, 
  product_add_schema, 
  product_update_schema, 
  product_delete_schema 
} = require('../schema/product');

// 获取商品列表
router.get('/list', express<PERSON>oi(product_list_schema), product_handler.getProductList);

// 获取商品详情
router.get('/detail', expressJoi(product_detail_schema), product_handler.getProductDetail);

// 新增商品
router.post('/add', expressJoi(product_add_schema), product_handler.addProduct);

// 更新商品
router.post('/update', expressJoi(product_update_schema), product_handler.updateProduct);

// 删除商品(伪删除)
router.post('/delete', expressJoi(product_delete_schema), product_handler.deleteProduct);

module.exports = router;
