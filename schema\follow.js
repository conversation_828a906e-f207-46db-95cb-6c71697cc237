const joi = require('joi');

// 获取跟进记录列表的验证规则
const follow_list_schema = {
  query: {
    businessType: joi.string().valid('department', 'position', 'job', 'process', 'permission', 'product').required(),
    businessId: joi.string().required(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取跟进记录详情的验证规则
const follow_detail_schema = {
  query: {
    id: joi.string().required()
  }
};

// 添加跟进记录的验证规则
const follow_add_schema = {
  body: {
    businessType: joi.string().valid('department', 'position', 'job', 'process', 'permission', 'product').required(),
    businessId: joi.string().required(),
    content: joi.string().required(),
    status: joi.string().valid('待处理', '进行中', '已完成', '已取消').default('待处理'),
    remark: joi.string().allow('').optional()
  }
};

// 更新跟进记录的验证规则
const follow_update_schema = {
  body: {
    id: joi.string().required(),
    content: joi.string().optional(),
    status: joi.string().valid('待处理', '进行中', '已完成', '已取消').optional(),
    remark: joi.string().allow('').optional(),
    deleteImageIds: joi.array().items(joi.string()).optional()
  }
};

// 删除跟进记录的验证规则
const follow_delete_schema = {
  body: {
    id: joi.string().required()
  }
};

// 获取业务对象跟进记录的验证规则
const follow_business_schema = {
  query: {
    businessType: joi.string().valid('department', 'position', 'job', 'process', 'permission', 'product').required(),
    businessId: joi.string().required(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取图片Base64的验证规则
const follow_image_base64_schema = {
  params: {
    imageId: joi.string().required()
  }
};

module.exports = {
  follow_list_schema,
  follow_detail_schema,
  follow_add_schema,
  follow_update_schema,
  follow_delete_schema,
  follow_business_schema,
  follow_image_base64_schema
};
