const db = require('../db/index')
const log = require('./log')

const getPostList = (req, res) => {
    try {
        const sql = 'select * from posts where delFlag=0';
        db.query(sql, (err, results) => {
            if (err) return res.output(err);
            res.output(null, 200, results);
        });
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const getPostById = (req, res) => {
    try {
        const id = req.query.id;
        const sql = 'select * from posts where id=?';
        db.query(sql, id, (err, results) => {
            if (err) return res.output(err);
            if (results.length !== 1) return res.output(new Error("获取岗位信息失败！"), 400);
            res.output(null, 200, results[0]);
        });
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const addPost = (req, res) => {
    try {
        const user = req.user;
        const { name, code, description } = req.body;
        const sql = 'insert into posts (name,code,description,createUserName) values (?,?,?,?)';
        db.query(sql, [name, code, description, user.username], (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error("添加岗位失败！"), 400);
            res.output(null, 200, { id: results.insertId,message: "添加岗位成功" }, true);
        });
        log.add("sysLog", "添加岗位:" + name, user.username);
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const updatePost = (req, res) => {
    try {
        const user = req.user;
        const { id, name, code, description } = req.body;
        const sql = 'update posts set name=?,code=?,description=?,modifyUserName=? where id=?';
        db.query(sql, [name, code, description, user.username, id], (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error("更新岗位失败！"), 400);
            res.output(null, 200, { message: "更新岗位成功" }, true);
        });
        log.add("sysLog", "更新岗位:" + name, user.username);
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const deletePost = (req, res) => {
    try {
        const user = req.user;
        const id = req.body.id;
        const sql = 'update posts set delFlag=1 where id=?';
        db.query(sql, id, (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error("删除岗位失败！"), 400);
            res.output(null, 200, { message: "删除岗位成功" }, true);
        });
        log.add("sysLog", "删除岗位:" + id, user.username);
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

module.exports = {
    getPostList,
    getPostById,
    addPost,
    updatePost,
    deletePost
} 