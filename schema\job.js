const joi = require('joi');

// 获取岗位列表的验证规则
const job_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取岗位详情的验证规则
const job_detail_schema = {
  query: {
    id: joi.number().integer().required()
  }
};

// 新增岗位的验证规则
const job_add_schema = {
  body: {
    name: joi.string().required(),
    code: joi.string().required(),
    status: joi.string().valid('1', '0').default('1')
  }
};

// 更新岗位的验证规则
const job_update_schema = {
  body: {
    id: joi.number().integer().required(),
    name: joi.string().required(),
    code: joi.string().required(),
    status: joi.string().valid('1', '0').default('1')
  }
};

// 删除岗位的验证规则
const job_delete_schema = {
  body: {
    id: joi.number().integer().required()
  }
};

module.exports = {
  job_list_schema,
  job_detail_schema,
  job_add_schema,
  job_update_schema,
  job_delete_schema
};
