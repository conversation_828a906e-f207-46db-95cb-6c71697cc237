{"_from": "assign-symbols@^1.0.0", "_id": "assign-symbols@1.0.0", "_inBundle": false, "_integrity": "sha512-Q+JC7Whu8HhmTdBph/Tq59IoRtoy6KAm5zzPv00WdujX82lbAL8K7WVjne7vdCsAmbF4AYaDOPyO3k0kl8qIrw==", "_location": "/assign-symbols", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "assign-symbols@^1.0.0", "name": "assign-symbols", "escapedName": "assign-symbols", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/extend-shallow"], "_resolved": "https://registry.npmjs.org/assign-symbols/-/assign-symbols-1.0.0.tgz", "_shasum": "59667f41fadd4f20ccbc2bb96b8d4f7f78ec0367", "_spec": "assign-symbols@^1.0.0", "_where": "D:\\Code\\node-express\\node_modules\\extend-shallow", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/assign-symbols/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Assign the enumerable es6 Symbol properties from an object (or objects) to the first object passed on the arguments. Can be used as a supplement to other extend, assign or merge methods as a polyfill for the Symbols part of the es6 Object.assign method.", "devDependencies": {"mocha": "^3.0.0"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/assign-symbols", "keywords": ["assign", "symbols"], "license": "MIT", "main": "index.js", "name": "assign-symbols", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/assign-symbols.git"}, "scripts": {"test": "mocha"}, "verb": {"related": {"list": ["assign-deep", "mixin-deep", "merge-deep", "extend-shallow", "clone-deep"]}}, "version": "1.0.0"}