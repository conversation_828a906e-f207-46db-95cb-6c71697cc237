{"_from": "entities@^6.0.0", "_id": "entities@6.0.0", "_inBundle": false, "_integrity": "sha512-aKstq2TDOndCn4diEyp9Uq/Flu2i1GlLkc6XIDQSDMuaFE3OPW5OphLCyQ5SpSJZTb4reN+kTcYru5yIfXoRPw==", "_location": "/parse5/entities", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "entities@^6.0.0", "name": "entities", "escapedName": "entities", "rawSpec": "^6.0.0", "saveSpec": null, "fetchSpec": "^6.0.0"}, "_requiredBy": ["/parse5"], "_resolved": "https://registry.npmmirror.com/entities/-/entities-6.0.0.tgz", "_shasum": "09c9e29cb79b0a6459a9b9db9efb418ac5bb8e51", "_spec": "entities@^6.0.0", "_where": "D:\\Code\\node-express\\node_modules\\parse5", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/entities/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Encode & decode XML and HTML entities with ease & speed", "devDependencies": {"@types/node": "^22.10.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.0", "@vitest/coverage-v8": "^2.1.8", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-n": "^17.15.1", "eslint-plugin-unicorn": "^56.0.1", "prettier": "^3.4.2", "tshy": "^3.0.2", "tsx": "^4.19.2", "typedoc": "^0.27.5", "typescript": "^5.7.2", "vitest": "^2.0.2"}, "engines": {"node": ">=0.12"}, "exports": {".": {"import": {"types": "./dist/esm/index.d.ts", "default": "./dist/esm/index.js"}, "require": {"types": "./dist/commonjs/index.d.ts", "default": "./dist/commonjs/index.js"}}, "./decode": {"import": {"types": "./dist/esm/decode.d.ts", "default": "./dist/esm/decode.js"}, "require": {"types": "./dist/commonjs/decode.d.ts", "default": "./dist/commonjs/decode.js"}}, "./escape": {"import": {"types": "./dist/esm/escape.d.ts", "default": "./dist/esm/escape.js"}, "require": {"types": "./dist/commonjs/escape.d.ts", "default": "./dist/commonjs/escape.js"}}}, "files": ["decode.js", "escape.js", "dist", "src"], "funding": "https://github.com/fb55/entities?sponsor=1", "homepage": "https://github.com/fb55/entities#readme", "keywords": ["html entities", "entity decoder", "entity encoding", "html decoding", "html encoding", "xml decoding", "xml encoding"], "license": "BSD-2-<PERSON><PERSON>", "main": "./dist/commonjs/index.js", "module": "./dist/esm/index.js", "name": "entities", "prettier": {"proseWrap": "always", "tabWidth": 4}, "repository": {"type": "git", "url": "git://github.com/fb55/entities.git"}, "scripts": {"build:docs": "typedoc --hideGenerator src/index.ts", "build:encode-trie": "node --import=tsx scripts/write-encode-map.ts", "build:trie": "node --import=tsx scripts/write-decode-map.ts", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:ts && npm run lint:prettier", "lint:es": "eslint . --ignore-path .gitignore", "lint:prettier": "npm run prettier -- --check", "lint:ts": "tsc --noEmit", "prepublishOnly": "tshy", "prettier": "prettier '**/*.{ts,md,json,yml}'", "test": "npm run test:vi && npm run lint", "test:vi": "vitest run"}, "sideEffects": false, "tshy": {"exclude": ["**/*.spec.ts", "**/__fixtures__/*", "**/__tests__/*", "**/__snapshots__/*"], "exports": {".": "./src/index.ts", "./decode": "./src/decode.ts", "./escape": "./src/escape.ts"}}, "type": "module", "types": "./dist/commonjs/index.d.ts", "version": "6.0.0"}