{"_from": "css-select@^5.1.0", "_id": "css-select@5.1.0", "_inBundle": false, "_integrity": "sha512-nwoRF1rvRRnnCqqY7updORDsuqKzqYJ28+oSMaJMMgOauh3fvwHqMS7EZpIPqK8GL+g9mKxF1vP/ZjSeNjEVHg==", "_location": "/css-select", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "css-select@^5.1.0", "name": "css-select", "escapedName": "css-select", "rawSpec": "^5.1.0", "saveSpec": null, "fetchSpec": "^5.1.0"}, "_requiredBy": ["/cheerio-select"], "_resolved": "https://registry.npmmirror.com/css-select/-/css-select-5.1.0.tgz", "_shasum": "b8ebd6554c3637ccc76688804ad3f6a6fdaea8a6", "_spec": "css-select@^5.1.0", "_where": "D:\\Code\\node-express\\node_modules\\cheerio-select", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/css-select/issues"}, "bundleDependencies": false, "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "deprecated": false, "description": "a CSS selector compiler/engine", "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.4.1", "@types/node": "^17.0.29", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "cheerio-soupselect": "^0.1.1", "eslint": "^8.14.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib"], "funding": {"url": "https://github.com/sponsors/fb55"}, "homepage": "https://github.com/fb55/css-select#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": "$1"}, "testMatch": ["<rootDir>/test/*.ts"]}, "keywords": ["css", "selector", "sizzle"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "module": "lib/esm/index.js", "name": "css-select", "prettier": {"tabWidth": 4, "proseWrap": "always"}, "repository": {"type": "git", "url": "git://github.com/fb55/css-select.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/css-select/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "prepare": "npm run build", "prettier": "prettier '**/*.{ts,md,json,yml}'", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "types": "lib/index.d.ts", "version": "5.1.0"}