const express = require('express');
const router = express.Router();
const department_handler = require('../router_handler/department');
const expressJoi = require('@escook/express-joi');
const {
  department_list_schema,
  department_detail_schema,
  department_add_schema,
  department_update_schema,
  department_delete_schema
} = require('../schema/department');

// 获取部门列表
router.get('/list', express<PERSON><PERSON>(department_list_schema),department_handler.getDepartmentList);

// 获取部门详情
router.get('/detail', expressJoi(department_detail_schema), department_handler.getDepartmentDetail);

// 新增部门
router.post('/add', expressJoi(department_add_schema), department_handler.addDepartment);

// 更新部门
router.post('/update', express<PERSON><PERSON>(department_update_schema), department_handler.updateDepartment);

// 删除部门(伪删除)
router.post('/delete', expressJoi(department_delete_schema), department_handler.deleteDepartment);

// Android应用专用：获取部门下拉框数据
router.get('/', department_handler.getDepartments);

module.exports = router;
