const joi = require('joi');

// 获取商品列表的验证规则
const product_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取商品详情的验证规则
const product_detail_schema = {
  query: {
    id: joi.string().required()
  }
};

// 新增商品的验证规则
const product_add_schema = {
  body: {
    name: joi.string().required(),
    code: joi.string().required(),
    category: joi.string().required(),
    price: joi.number().min(0).required(),
    stock: joi.number().integer().min(0).required(),
    status: joi.string().valid('正常', '停用').default('正常'),
    description: joi.string().allow('').optional(),
    specifications: joi.object().optional(),
    images: joi.array().items(joi.string()).optional()
  }
};

// 更新商品的验证规则
const product_update_schema = {
  body: {
    id: joi.string().required(),
    name: joi.string().required(),
    code: joi.string().required(),
    category: joi.string().required(),
    price: joi.number().min(0).required(),
    stock: joi.number().integer().min(0).required(),
    status: joi.string().valid('正常', '停用').default('正常'),
    description: joi.string().allow('').optional(),
    specifications: joi.object().optional(),
    images: joi.array().items(joi.string()).optional()
  }
};

// 删除商品的验证规则
const product_delete_schema = {
  body: {
    id: joi.string().required()
  }
};

module.exports = {
  product_list_schema,
  product_detail_schema,
  product_add_schema,
  product_update_schema,
  product_delete_schema
};
