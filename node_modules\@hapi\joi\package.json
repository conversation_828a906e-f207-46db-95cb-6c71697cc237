{"_from": "@hapi/joi@^17.1.1", "_id": "@hapi/joi@17.1.1", "_inBundle": false, "_integrity": "sha512-p4DKeZAoeZW4g3u7ZeRo+vCDuSDgSvtsB/NpfjXEHTUjSeINAi/RrVOWiVQ1isaoLzMvFEhe8n5065mQq1AdQg==", "_location": "/@hapi/joi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@hapi/joi@^17.1.1", "name": "@hapi/joi", "escapedName": "@hapi%2fjoi", "scope": "@hapi", "rawSpec": "^17.1.1", "saveSpec": null, "fetchSpec": "^17.1.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@hapi/joi/-/joi-17.1.1.tgz", "_shasum": "9cc8d7e2c2213d1e46708c6260184b447c661350", "_spec": "@hapi/joi@^17.1.1", "_where": "D:\\Code\\node-express", "browser": "dist/joi-browser.min.js", "bugs": {"url": "https://github.com/hapijs/joi/issues"}, "bundleDependencies": false, "dependencies": {"@hapi/address": "^4.0.1", "@hapi/formula": "^2.0.0", "@hapi/hoek": "^9.0.0", "@hapi/pinpoint": "^2.0.0", "@hapi/topo": "^5.0.0"}, "deprecated": "Switch to 'npm install joi'", "description": "Object schema validation", "devDependencies": {"@hapi/bourne": "2.x.x", "@hapi/code": "8.x.x", "@hapi/joi-legacy-test": "npm:@hapi/joi@15.x.x", "@hapi/lab": "22.x.x"}, "files": ["lib/**/*", "dist/*"], "homepage": "https://github.com/hapijs/joi#readme", "keywords": ["schema", "validation"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@hapi/joi", "repository": {"type": "git", "url": "git://github.com/hapijs/joi.git"}, "scripts": {"prepublishOnly": "cd browser && npm install && npm run build", "test": "lab -t 100 -a @hapi/code -L", "test-cov-html": "lab -r html -o coverage.html -a @hapi/code"}, "version": "17.1.1"}