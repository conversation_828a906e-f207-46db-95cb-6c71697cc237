{"_from": "@sideway/address@^4.1.5", "_id": "@sideway/address@4.1.5", "_inBundle": false, "_integrity": "sha512-IqO/DUQHUkPeixNQ8n0JA6102hT9CmaljNTPmQ1u8MEhBo/R4Q8eKLN/vGZxuebwOroDB4cbpjheD4+/sKFK4Q==", "_location": "/@sideway/address", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@sideway/address@^4.1.5", "name": "@sideway/address", "escapedName": "@sideway%2faddress", "scope": "@sideway", "rawSpec": "^4.1.5", "saveSpec": null, "fetchSpec": "^4.1.5"}, "_requiredBy": ["/joi"], "_resolved": "https://registry.npmjs.org/@sideway/address/-/address-4.1.5.tgz", "_shasum": "4bc149a0076623ced99ca8208ba780d65a99b9d5", "_spec": "@sideway/address@^4.1.5", "_where": "D:\\Code\\node-express\\node_modules\\joi", "bugs": {"url": "https://github.com/sideway/address/issues"}, "bundleDependencies": false, "dependencies": {"@hapi/hoek": "^9.0.0"}, "deprecated": false, "description": "Email address and domain validation", "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "24.x.x", "typescript": "4.0.x"}, "files": ["lib"], "homepage": "https://github.com/sideway/address#readme", "keywords": ["email", "domain", "address", "validation"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@sideway/address", "repository": {"type": "git", "url": "git://github.com/sideway/address.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "types": "lib/index.d.ts", "version": "4.1.5"}