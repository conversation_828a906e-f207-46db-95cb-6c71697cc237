{"_from": "array-flatten@3.0.0", "_id": "array-flatten@3.0.0", "_inBundle": false, "_integrity": "sha512-zPMVc3ZYlGLNk4mpK1NzP2wg0ml9t7fUgDsayR5Y5rSzxQilzR9FGu/EH2jQOcKSAeAfWeylyW8juy3OkWRvNA==", "_location": "/array-flatten", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "array-flatten@3.0.0", "name": "array-flatten", "escapedName": "array-flatten", "rawSpec": "3.0.0", "saveSpec": null, "fetchSpec": "3.0.0"}, "_requiredBy": ["/router"], "_resolved": "https://registry.npmjs.org/array-flatten/-/array-flatten-3.0.0.tgz", "_shasum": "6428ca2ee52c7b823192ec600fa3ed2f157cd541", "_spec": "array-flatten@3.0.0", "_where": "D:\\Code\\node-express\\node_modules\\router", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://blakeembrey.me"}, "bugs": {"url": "https://github.com/blakeembrey/array-flatten/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Flatten nested arrays", "devDependencies": {"@size-limit/preset-small-lib": "^2.2.1", "@types/jest": "^24.0.23", "@types/node": "^12.12.11", "benchmarked": "^2.0.0", "husky": "^3.1.0", "jest": "^24.9.0", "lint-staged": "^9.4.3", "prettier": "^1.19.1", "ts-expect": "^1.1.0", "ts-jest": "^24.1.0", "tslint": "^5.20.1", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "typescript": "^3.7.2"}, "files": ["dist/", "dist.es2015/", "LICENSE"], "homepage": "https://github.com/blakeembrey/array-flatten", "husky": {"hooks": {"pre-commit": "lint-staged"}}, "jest": {"roots": ["<rootDir>/src/"], "transform": {"\\.tsx?$": "ts-jest"}, "testRegex": "(/__tests__/.*|\\.(test|spec))\\.(tsx?|jsx?)$", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}, "jsnext:main": "dist.es2015/index.js", "keywords": ["array", "flatten", "arguments", "depth", "fast", "for"], "license": "MIT", "lint-staged": {"*.{js,json,css,md}": ["npm run prettier", "git add"]}, "main": "dist/index.js", "module": "dist.es2015/index.js", "name": "array-flatten", "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git://github.com/blakeembrey/array-flatten.git"}, "scripts": {"benchmark": "node benchmark", "build": "rimraf dist/ dist.es2015/ && tsc && tsc -P tsconfig.es2015.json", "format": "npm run prettier -- \"{.,src/**,benchmark/**}/*.{js,ts}\"", "lint": "tslint \"src/**/*\" --project tsconfig.json", "prepare": "npm run build", "prettier": "prettier --write", "size": "size-limit", "specs": "jest --coverage", "test": "npm run build && npm run lint && npm run specs && npm run size"}, "sideEffects": false, "size-limit": [{"path": "dist/index.js", "limit": "100 B"}], "typings": "dist/index.d.ts", "version": "3.0.0"}