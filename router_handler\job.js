const db = require('../db/index')
const log = require('./log')
// 获取岗位列表
const getJobList = (req, res) => {
  try {
    const user = req.user;
    const { keyword = '', page = 1, size = 10 } = req.query;
    console.log("查询参数:", { page, size, keyword });

    // 构建SQL查询
    let extSql = "";
    let params = [];

    if (keyword && keyword.trim() !== '') {
      extSql = "AND (`name` LIKE ? OR 'code' LIKE ?) ";
      const searchTerm = `%${keyword}%`;
      params = [searchTerm, searchTerm, searchTerm];
    }

    // 计算分页
    const offset = (page - 1) * size;

    // 查询总数
    const countSql = "SELECT COUNT(*) as total FROM posts WHERE  `status` = 1 " + extSql;
    db.query(countSql, params, (countErr, countResults) => {
      if (countErr) {
        console.error("查询总数出错:", countErr);
        return res.output(countErr, 500);
      }

      const total = countResults[0].total;

      // 查询数据
      const dataSql = "SELECT * FROM posts WHERE `status` = 1 " + extSql + " LIMIT ?, ?";
      const dataParams = [...params, offset, size];
      db.query(dataSql, dataParams, (err, results) => {
        if (err) {
          console.error("查询数据出错:", err);
          return res.output(err, 500);
        }

        // 返回结果
        res.output(null, 200, {
          total: total,
          page: page,
          size: size,
          list: results
        }, true);
      });
    });
    // 记录日志
    log.add("sysLog", "查询岗位列表:" + keyword, user.username);

  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取岗位详情
const getJobDetail = (req, res) => {
  try {
    const { id } = req.query;
    const sql = 'select * from posts where id= ?'
    db.query(sql, id, (err, results) => {
      if (err) return res.output(err)
      if (results.length !== 1) return res.output(new Error("获取职务信息失败！"), 400)
      res.output(null, 200, results[0], true);
    })
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 新增岗位
const addJob = (req, res) => {
  try {
    const user = req.user;
    const { name, code, status } = req.body;
    const sqlStr = "insert into posts set ?";
    db.query(sqlStr, { name, code, status }, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("新增岗位失败！"), 400);
      res.output(null, 200, { id: results.insertId,message: "新增岗位成功" });
    });
    log.add("sysLog", "新增岗位信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 更新岗位
const updateJob = (req, res) => {
  try {
    const { id, name, code, status } = req.body;
    const user = req.user;
    const sqlStr = 'update posts set name=?,code=?,status=? where id =?'
    db.query(sqlStr, [name, code, status, id], (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('更新岗位信息失败！'), 400)
      res.output(null, 200, { id: id, name: name, code: code }, true)
    })
    log.add("sysLog", "修改岗位信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 删除岗位
const deleteJob = (req, res) => {
  try {
    const { id } = req.body;
    const user = req.user;

    const sqlStr = 'update posts set delFlag = 1 where id =?'
    db.query(sqlStr, id, (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('删除岗位失败！'), 400)
      res.output(null, 200, { id: id }, true)
    })
    log.add("sysLog", "删除岗位信息:" + id, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// Android应用专用：获取岗位下拉框数据
const getPosts = (req, res) => {
  try {
    // 查询数据
    const dataSql = "SELECT * FROM posts WHERE delFlag=0";
    db.query(dataSql, null, (err, results) => {
      if (err) {
        console.error("查询数据出错:", err);
        return res.output(err, 500);
      }
      // 返回结果
      res.output(null, 200, results, true);
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

module.exports = {
  getJobList,
  getJobDetail,
  addJob,
  updateJob,
  deleteJob,
  getPosts
};
