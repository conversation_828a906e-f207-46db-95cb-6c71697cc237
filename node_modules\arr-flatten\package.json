{"_from": "arr-flatten@^1.0.1", "_id": "arr-flatten@1.1.0", "_inBundle": false, "_integrity": "sha512-L3hKV5R/p5o81R7O02IGnwpDmkp6E982XhtbuwSe3O4qOtMMMtodicASA1Cny2U+aCXcNpml+m4dPsvsJ3jatg==", "_location": "/arr-flatten", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "arr-flatten@^1.0.1", "name": "arr-flatten", "escapedName": "arr-flatten", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/arr-diff", "/server-static/braces"], "_resolved": "https://registry.npmjs.org/arr-flatten/-/arr-flatten-1.1.0.tgz", "_shasum": "36048bbff4e7b47e136644316c99669ea5ae91f1", "_spec": "arr-flatten@^1.0.1", "_where": "D:\\Code\\node-express\\node_modules\\arr-diff", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/arr-flatten/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "http://twitter.com/jonschlinkert"}, {"name": "<PERSON>", "url": "https://lukeed.com"}], "deprecated": false, "description": "Recursively flatten an array or arrays.", "devDependencies": {"ansi-bold": "^0.1.1", "array-flatten": "^2.1.1", "array-slice": "^1.0.0", "benchmarked": "^1.0.0", "compute-flatten": "^1.0.0", "flatit": "^1.1.1", "flatten": "^1.0.2", "flatten-array": "^1.0.0", "glob": "^7.1.1", "gulp-format-md": "^0.1.12", "just-flatten-it": "^1.1.23", "lodash.flattendeep": "^4.4.0", "m_flattened": "^1.0.1", "mocha": "^3.2.0", "utils-flatten": "^1.0.0", "write": "^0.3.3"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/arr-flatten", "keywords": ["arr", "array", "elements", "flat", "flatten", "nested", "recurse", "recursive", "recursively"], "license": "MIT", "main": "index.js", "name": "arr-flatten", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/arr-flatten.git"}, "scripts": {"test": "mocha"}, "verb": {"toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-filter", "arr-union", "array-each", "array-unique"]}, "lint": {"reflinks": true}}, "version": "1.1.0"}