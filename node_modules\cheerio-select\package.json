{"_from": "cheerio-select@^2.1.0", "_id": "cheerio-select@2.1.0", "_inBundle": false, "_integrity": "sha512-9v9kG0LvzrlcungtnJtpGNxY+fzECQKhK4EGJX2vByejiMX84MFNQw4UxPJl3bFbTMw+Dfs37XaIkCwTZfLh4g==", "_location": "/cheerio-select", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cheerio-select@^2.1.0", "name": "cheerio-select", "escapedName": "cheerio-select", "rawSpec": "^2.1.0", "saveSpec": null, "fetchSpec": "^2.1.0"}, "_requiredBy": ["/cheerio"], "_resolved": "https://registry.npmmirror.com/cheerio-select/-/cheerio-select-2.1.0.tgz", "_shasum": "4d8673286b8126ca2a8e42740d5e3c4884ae21b4", "_spec": "cheerio-select@^2.1.0", "_where": "D:\\Code\\node-express\\node_modules\\cheerio", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/cheeriojs/cheerio-select/issues"}, "bundleDependencies": false, "dependencies": {"boolbase": "^1.0.0", "css-select": "^5.1.0", "css-what": "^6.1.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.0.1"}, "deprecated": false, "description": "CSS selector engine supporting jQuery selectors", "devDependencies": {"@types/boolbase": "^1.0.1", "@types/jest": "^27.5.0", "@types/node": "^17.0.33", "@typescript-eslint/eslint-plugin": "^5.23.0", "@typescript-eslint/parser": "^5.23.0", "eslint": "^8.15.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.1", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.4"}, "directories": {"lib": "lib/"}, "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib/**/*"], "funding": {"url": "https://github.com/sponsors/fb55"}, "homepage": "https://github.com/cheeriojs/cheerio-select#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "module": "lib/esm/index.js", "name": "cheerio-select", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git://github.com/cheeriojs/cheerio-select.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/cheeriojs/cheerio-select/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "prepare": "npm run build", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "2.1.0"}