{"_from": "async-each@^1.0.0", "_id": "async-each@1.0.6", "_inBundle": false, "_integrity": "sha512-c646jH1avxr+aVpndVMeAfYw7wAa6idufrlN3LPA4PmKS0QEGp6PIC9nwz0WQkkvBGAMEki3pFdtxaF39J9vvg==", "_location": "/async-each", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "async-each@^1.0.0", "name": "async-each", "escapedName": "async-each", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/server-static/chokidar"], "_resolved": "https://registry.npmjs.org/async-each/-/async-each-1.0.6.tgz", "_shasum": "52f1d9403818c179b7561e11a5d1b77eb2160e77", "_spec": "async-each@^1.0.0", "_where": "D:\\Code\\node-express\\node_modules\\server-static\\node_modules\\chokidar", "author": {"name": "<PERSON>", "url": "https://paulmillr.com/"}, "bugs": {"url": "https://github.com/paulmillr/async-each/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "No-bullshit, ultra-simple, 35-lines-of-code async parallel forEach / map function for JavaScript.", "files": ["index.js"], "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "homepage": "https://github.com/paulmillr/async-each/", "keywords": ["async", "for<PERSON>ach", "each", "map", "asynchronous", "iteration", "iterate", "loop", "parallel", "concurrent", "array", "flow", "control flow"], "license": "MIT", "main": "index.js", "name": "async-each", "repository": {"type": "git", "url": "git+https://github.com/paulmillr/async-each.git"}, "version": "1.0.6"}