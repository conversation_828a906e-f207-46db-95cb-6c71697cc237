const joi = require('joi');

// 获取部门列表的验证规则
const department_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取部门详情的验证规则
const department_detail_schema = {
  query: {
    id: joi.number().integer().required()
  }
};

// 新增部门的验证规则
const department_add_schema = {
  body: {
    name: joi.string().required(),
    code: joi.string().required(),
    parentCode: joi.string().default('0')
  }
};

// 更新部门的验证规则
const department_update_schema = {
  body: {
    id: joi.number().integer().required(),
    name: joi.string().required(),
    code: joi.string().required(),
    parentCode: joi.string().default('0')
  }
};

// 删除部门的验证规则
const department_delete_schema = {
  body: {
    id: joi.number().integer().required()
  }
};

module.exports = {
  department_list_schema,
  department_detail_schema,
  department_add_schema,
  department_update_schema,
  department_delete_schema
};
