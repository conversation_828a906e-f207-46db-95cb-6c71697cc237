{"_from": "parse5@^7.0.0", "_id": "parse5@7.3.0", "_inBundle": false, "_integrity": "sha512-IInvU7fabl34qmi9gY8XOVxhYyMyuH2xUNpb2q8/Y+7552KlejkRvqvD19nMoUW/uQGGbqNpA6Tufu5FL5BZgw==", "_location": "/parse5", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "parse5@^7.0.0", "name": "parse5", "escapedName": "parse5", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/cheerio", "/parse5-htmlparser2-tree-adapter"], "_resolved": "https://registry.npmmirror.com/parse5/-/parse5-7.3.0.tgz", "_shasum": "d7e224fa72399c7a175099f45fc2ad024b05ec05", "_spec": "parse5@^7.0.0", "_where": "D:\\Code\\node-express\\node_modules\\cheerio", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/inikulin"}, "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "bundleDependencies": false, "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "dependencies": {"entities": "^6.0.0"}, "deprecated": false, "description": "HTML parser and serializer.", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "files": ["dist/cjs/package.json", "dist/**/*.js", "dist/**/*.d.ts"], "funding": "https://github.com/inikulin/parse5?sponsor=1", "homepage": "https://parse5.js.org", "keywords": ["html", "parser", "html5", "WHATWG", "specification", "fast", "html parser", "html5 parser", "htmlparser", "parse5", "serializer", "html serializer", "htmlserializer", "parse", "serialize"], "license": "MIT", "main": "dist/cjs/index.js", "module": "dist/index.js", "name": "parse5", "repository": {"type": "git", "url": "git://github.com/inikulin/parse5.git"}, "scripts": {"build:cjs": "tsc --noCheck --moduleResolution node10 --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "type": "module", "types": "dist/index.d.ts", "version": "7.3.0"}