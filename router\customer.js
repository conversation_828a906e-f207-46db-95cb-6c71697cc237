const express = require('express');
const router = express.Router();
const expressJoi = require('@escook/express-joi');
const customer_handler = require('../router_handler/customer');

const {
    customer_list_schema,
    customer_detail_schema,
    customer_add_schema,
    customer_update_schema,
    customer_delete_schema
} = require('../schema/customer');

// 获取所有
router.get('/list', expressJoi(customer_list_schema), customer_handler.getCustomerList);
// 获取详情
router.get('/detail', expressJoi(customer_detail_schema), customer_handler.getCustomerDetail);

// 新增
router.post('/add', expressJoi(customer_add_schema), customer_handler.addCustomer);

// 更新
router.post('/update', expressJoi(customer_update_schema), customer_handler.updateCustomer);

// 删除(伪删除)
router.post('/delete', expressJoi(customer_delete_schema), customer_handler.deleteCustomer);
module.exports = router;
