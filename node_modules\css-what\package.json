{"_from": "css-what@^6.1.0", "_id": "css-what@6.1.0", "_inBundle": false, "_integrity": "sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==", "_location": "/css-what", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "css-what@^6.1.0", "name": "css-what", "escapedName": "css-what", "rawSpec": "^6.1.0", "saveSpec": null, "fetchSpec": "^6.1.0"}, "_requiredBy": ["/cheerio-select", "/css-select"], "_resolved": "https://registry.npmmirror.com/css-what/-/css-what-6.1.0.tgz", "_shasum": "fb5effcf76f1ddea2c81bdfaa4de44e79bac70f4", "_spec": "css-what@^6.1.0", "_where": "D:\\Code\\node-express\\node_modules\\cheerio-select", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://feedic.com"}, "bugs": {"url": "https://github.com/fb55/css-what/issues"}, "bundleDependencies": false, "deprecated": false, "description": "a CSS selector parser", "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.23", "@typescript-eslint/eslint-plugin": "^5.17.0", "@typescript-eslint/parser": "^5.17.0", "eslint": "^8.12.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-node": "^11.1.0", "jest": "^27.5.1", "prettier": "^2.6.1", "ts-jest": "^27.1.4", "typescript": "^4.6.3"}, "engines": {"node": ">= 6"}, "files": ["lib/**/*"], "funding": {"url": "https://github.com/sponsors/fb55"}, "homepage": "https://github.com/fb55/css-what#readme", "jest": {"preset": "ts-jest", "roots": ["src"]}, "license": "BSD-2-<PERSON><PERSON>", "main": "lib/commonjs/index.js", "module": "lib/es/index.js", "name": "css-what", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git+https://github.com/fb55/css-what.git"}, "scripts": {"build": "tsc && tsc -p tsconfig.es.json", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run prettier -- --check", "prepare": "npm run build", "prettier": "prettier '**/*.{ts,md,json,yml}'", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/es/index.d.ts", "version": "6.1.0"}