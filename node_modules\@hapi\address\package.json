{"_from": "@hapi/address@^4.0.1", "_id": "@hapi/address@4.1.0", "_inBundle": false, "_integrity": "sha512-SkszZf13HVgGmChdHo/PxchnSaCJ6cetVqLzyciudzZRT0jcOouIF/Q93mgjw8cce+D+4F4C1Z/WrfFN+O3VHQ==", "_location": "/@hapi/address", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@hapi/address@^4.0.1", "name": "@hapi/address", "escapedName": "@hapi%2faddress", "scope": "@hapi", "rawSpec": "^4.0.1", "saveSpec": null, "fetchSpec": "^4.0.1"}, "_requiredBy": ["/@hapi/joi"], "_resolved": "https://registry.npmjs.org/@hapi/address/-/address-4.1.0.tgz", "_shasum": "d60c5c0d930e77456fdcde2598e77302e2955e1d", "_spec": "@hapi/address@^4.0.1", "_where": "D:\\Code\\node-express\\node_modules\\@hapi\\joi", "bugs": {"url": "https://github.com/hapijs/address/issues"}, "bundleDependencies": false, "dependencies": {"@hapi/hoek": "^9.0.0"}, "deprecated": "Moved to 'npm install @sideway/address'", "description": "Email address and domain validation", "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "22.x.x"}, "files": ["lib"], "homepage": "https://github.com/hapijs/address#readme", "keywords": ["email", "domain", "address", "validation"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@hapi/address", "repository": {"type": "git", "url": "git://github.com/hapijs/address.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "types": "lib/index.d.ts", "version": "4.1.0"}