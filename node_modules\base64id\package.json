{"_from": "base64id@1.0.0", "_id": "base64id@1.0.0", "_inBundle": false, "_integrity": "sha512-rz8L+d/xByiB/vLVftPkyY215fqNrmasrcJsYkVcm4TgJNz+YXKrFaFAWibSaHkiKoSgMDCb+lipOIRQNGYesw==", "_location": "/base64id", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "base64id@1.0.0", "name": "base64id", "escapedName": "base64id", "rawSpec": "1.0.0", "saveSpec": null, "fetchSpec": "1.0.0"}, "_requiredBy": ["/engine.io"], "_resolved": "https://registry.npmjs.org/base64id/-/base64id-1.0.0.tgz", "_shasum": "47688cb99bb6804f0e06d3e763b1c32e57d8e6b6", "_spec": "base64id@1.0.0", "_where": "D:\\Code\\node-express\\node_modules\\engine.io", "author": {"name": "<PERSON><PERSON>", "email": "fael<PERSON><EMAIL>"}, "bugs": {"url": "https://github.com/faeldt/base64id/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Generates a base64 id", "engines": {"node": ">= 0.4.0"}, "homepage": "https://github.com/faeldt/base64id#readme", "license": "MIT", "main": "./lib/base64id.js", "name": "base64id", "repository": {"type": "git", "url": "git+https://github.com/faeldt/base64id.git"}, "version": "1.0.0"}