{"_from": "accepts@~1.3.8", "_id": "accepts@1.3.8", "_inBundle": false, "_integrity": "sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==", "_location": "/express/accepts", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "accepts@~1.3.8", "name": "accepts", "escapedName": "accepts", "rawSpec": "~1.3.8", "saveSpec": null, "fetchSpec": "~1.3.8"}, "_requiredBy": ["/express"], "_resolved": "https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz", "_shasum": "0bf0be125b67014adcb0b0921e62db7bffe16b2e", "_spec": "accepts@~1.3.8", "_where": "D:\\Code\\node-express\\node_modules\\express", "bugs": {"url": "https://github.com/jshttp/accepts/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://jongleberry.com"}], "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "deprecated": false, "description": "Higher-level content negotiation", "devDependencies": {"deep-equal": "1.0.1", "eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.3.1", "eslint-plugin-standard": "4.1.0", "mocha": "9.2.0", "nyc": "15.1.0"}, "engines": {"node": ">= 0.6"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/jshttp/accepts#readme", "keywords": ["content", "negotiation", "accept", "accepts"], "license": "MIT", "name": "accepts", "repository": {"type": "git", "url": "git+https://github.com/jshttp/accepts.git"}, "scripts": {"lint": "eslint .", "test": "mocha --reporter spec --check-leaks --bail test/", "test-ci": "nyc --reporter=lcov --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test"}, "version": "1.3.8"}