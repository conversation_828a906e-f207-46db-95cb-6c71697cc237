const joi = require('joi');

const mapping_list_schema = {
    query: {
        keyword: joi.string().allow('').optional(),
        page: joi.number().integer().min(1).default(1),
        size: joi.number().integer().min(1).max(100).default(10)
    }
};

const mapping_detail_schema = {
    query: {
        id: joi.number().integer().required()
    }
};

const mapping_add_schema = {
    body: {
        processId: joi.number().integer().required(),
        postId: joi.number().integer().required(),
        status: joi.string().valid('1', '0').default('1'),
        remark: joi.string().allow('').optional()
    }
};

const mapping_update_schema = {
    body: {
        id: joi.number().integer().required(),
        processId: joi.number().integer().required(),
        postId: joi.number().integer().required(),
        status: joi.string().valid('1', '0').default('1'),
        remark: joi.string().allow('').optional()
    }
};

const mapping_delete_schema = {
    body: {
        id: joi.number().integer().required()
    }
};

module.exports = {
    mapping_list_schema,
    mapping_detail_schema,
    mapping_add_schema,
    mapping_update_schema,
    mapping_delete_schema
};
