const db = require('../db/index')
const log = require('./log')

const getRoleList = (req, res) => {
    try {
        const sql = 'select * from roles where delFlag=0';
        db.query(sql, (err, results) => {
            if (err) return res.output(err);
            res.output(null, 200, results);
        });
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const getRoleById = (req, res) => {
    try {
        const id = req.query.id;
        const sql = 'select * from roles where id=?';
        db.query(sql, id, (err, results) => {
            if (err) return res.output(err);
            if (results.length !== 1) return res.output(new Error("获取角色信息失败！"), 400);
            res.output(null, 200, results[0]);
        });
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const addRole = (req, res) => {
    try {
        const user = req.user;
        const { name, code, description, permissions } = req.body;
        const sql = 'insert into roles (name,code,description,permissions,createUserName) values (?,?,?,?,?)';
        db.query(sql, [name, code, description, permissions, user.username], (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error("添加角色失败！"), 400);
            res.output(null, 200, { id: results.insertId,message: "添加角色成功" }, true);
        });
        log.add("sysLog", "添加角色:" + name, user.username);
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const updateRole = (req, res) => {
    try {
        const user = req.user;
        const { id, name, code, description, permissions } = req.body;
        const sql = 'update roles set name=?,code=?,description=?,permissions=?,modifyUserName=? where id=?';
        db.query(sql, [name, code, description, permissions, user.username, id], (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error("更新角色失败！"), 400);
            res.output(null, 200, { message: "更新角色成功" }, true);
        });
        log.add("sysLog", "更新角色:" + name, user.username);
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const deleteRole = (req, res) => {
    try {
        const user = req.user;
        const id = req.body.id;
        const sql = 'update roles set delFlag=1 where id=?';
        db.query(sql, id, (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error("删除角色失败！"), 400);
            res.output(null, 200, { message: "删除角色成功" }, true);
        });
        log.add("sysLog", "删除角色:" + id, user.username);
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

module.exports = {
    getRoleList,
    getRoleById,
    addRole,
    updateRole,
    deleteRole
} 