{"_from": "domutils@^3.0.1", "_id": "domutils@3.2.2", "_inBundle": false, "_integrity": "sha512-6kZKyUajlDuqlHKVX1w7gyslj9MPIXzIFiz/rGu35uC1wMi+kMhQwGhl4lt9unC9Vb9INnY9Z3/ZA3+FhASLaw==", "_location": "/domutils", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "domutils@^3.0.1", "name": "domutils", "escapedName": "domutils", "rawSpec": "^3.0.1", "saveSpec": null, "fetchSpec": "^3.0.1"}, "_requiredBy": ["/cheerio", "/cheerio-select", "/css-select", "/htmlparser2"], "_resolved": "https://registry.npmmirror.com/domutils/-/domutils-3.2.2.tgz", "_shasum": "edbfe2b668b0c1d97c24baf0f1062b132221bc78", "_spec": "domutils@^3.0.1", "_where": "D:\\Code\\node-express\\node_modules\\cheerio", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/domutils/issues"}, "bundleDependencies": false, "dependencies": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}, "deprecated": false, "description": "Utilities for working with htmlparser2's dom", "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.10.5", "@typescript-eslint/eslint-plugin": "^8.19.0", "@typescript-eslint/parser": "^8.19.0", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsdoc": "^50.6.1", "htmlparser2": "~9.1.0", "jest": "^29.7.0", "prettier": "^3.4.2", "ts-jest": "^29.2.5", "typedoc": "^0.27.6", "typescript": "^5.7.2"}, "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib/**/*"], "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}, "homepage": "https://github.com/fb55/domutils#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": ["$1.js", "$1"]}}, "keywords": ["dom", "htmlparser2"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "module": "lib/esm/index.js", "name": "domutils", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git://github.com/fb55/domutils.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/domutils/$(git rev-parse HEAD)/src/", "build:docs": "typedoc src", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint --ignore-path .gitignore .", "lint:prettier": "npm run prettier -- --check", "prepare": "npm run build", "prettier": "prettier \"**/*.{ts,md,json,yml}\" --ignore-path .gitignore", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "3.2.2"}