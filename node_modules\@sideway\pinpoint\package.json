{"_from": "@sideway/pinpoint@^2.0.0", "_id": "@sideway/pinpoint@2.0.0", "_inBundle": false, "_integrity": "sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ==", "_location": "/@sideway/pinpoint", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@sideway/pinpoint@^2.0.0", "name": "@sideway/pinpoint", "escapedName": "@sideway%2fpinpoint", "scope": "@sideway", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/joi"], "_resolved": "https://registry.npmjs.org/@sideway/pinpoint/-/pinpoint-2.0.0.tgz", "_shasum": "cff8ffadc372ad29fd3f78277aeb29e632cc70df", "_spec": "@sideway/pinpoint@^2.0.0", "_where": "D:\\Code\\node-express\\node_modules\\joi", "bugs": {"url": "https://github.com/sideway/pinpoint/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Return the filename and line number of the calling function", "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "24.x.x", "typescript": "4.0.x"}, "files": ["lib"], "homepage": "https://github.com/sideway/pinpoint#readme", "keywords": ["utilities"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@sideway/pinpoint", "repository": {"type": "git", "url": "git://github.com/sideway/pinpoint.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "types": "lib/index.d.ts", "version": "2.0.0"}