{"_from": "boolbase@^1.0.0", "_id": "boolbase@1.0.0", "_inBundle": false, "_integrity": "sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==", "_location": "/boolbase", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "boolbase@^1.0.0", "name": "boolbase", "escapedName": "boolbase", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/cheerio-select", "/css-select", "/nth-check"], "_resolved": "https://registry.npmmirror.com/boolbase/-/boolbase-1.0.0.tgz", "_shasum": "68dff5fbe60c51eb37725ea9e3ed310dcc1e776e", "_spec": "boolbase@^1.0.0", "_where": "D:\\Code\\node-express\\node_modules\\cheerio-select", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/boolbase/issues"}, "bundleDependencies": false, "deprecated": false, "description": "two functions: One that returns true, one that returns false", "homepage": "https://github.com/fb55/boolbase", "keywords": ["boolean", "function"], "license": "ISC", "main": "index.js", "name": "boolbase", "repository": {"type": "git", "url": "git+https://github.com/fb55/boolbase.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "version": "1.0.0"}