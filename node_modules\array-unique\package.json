{"_from": "array-unique@^0.2.1", "_id": "array-unique@0.2.1", "_inBundle": false, "_integrity": "sha512-G2n5bG5fSUCpnsXz4+8FUkYsGPkNfLn9YvS66U5qbTIXI2Ynnlo4Bi42bWv+omKUCqz+ejzfClwne0alJWJPhg==", "_location": "/array-unique", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "array-unique@^0.2.1", "name": "array-unique", "escapedName": "array-unique", "rawSpec": "^0.2.1", "saveSpec": null, "fetchSpec": "^0.2.1"}, "_requiredBy": ["/micromatch"], "_resolved": "https://registry.npmjs.org/array-unique/-/array-unique-0.2.1.tgz", "_shasum": "a1d97ccafcbc2625cc70fadceb36a50c58b01a53", "_spec": "array-unique@^0.2.1", "_where": "D:\\Code\\node-express\\node_modules\\micromatch", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/array-unique/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Return an array free of duplicate values. Fastest ES5 implementation.", "devDependencies": {"array-uniq": "^1.0.2", "benchmarked": "^0.1.3", "mocha": "*", "should": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/array-unique", "license": {"type": "MIT", "url": "https://github.com/jonschlinkert/array-unique/blob/master/LICENSE"}, "main": "index.js", "name": "array-unique", "repository": {"type": "git", "url": "git://github.com/jonschlinkert/array-unique.git"}, "scripts": {"test": "mocha"}, "version": "0.2.1"}