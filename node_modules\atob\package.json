{"_from": "atob@^2.1.2", "_id": "atob@2.1.2", "_inBundle": false, "_integrity": "sha512-Wm6ukoaOGJi/73p/cl2GvLjTI5JM1k/O14isD73YML8StrH/7/lRFgmg8nICZgD3bZZvjwCGxtMOD3wWNAu8cg==", "_location": "/atob", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "atob@^2.1.2", "name": "atob", "escapedName": "atob", "rawSpec": "^2.1.2", "saveSpec": null, "fetchSpec": "^2.1.2"}, "_requiredBy": ["/source-map-resolve"], "_resolved": "https://registry.npmjs.org/atob/-/atob-2.1.2.tgz", "_shasum": "6d9517eb9e030d2436666651e86bd9f6f13533c9", "_spec": "atob@^2.1.2", "_where": "D:\\Code\\node-express\\node_modules\\source-map-resolve", "author": {"name": "AJ <PERSON>", "email": "<EMAIL>", "url": "https://coolaj86.com"}, "bin": {"atob": "bin/atob.js"}, "browser": "browser-atob.js", "bundleDependencies": false, "deprecated": false, "description": "atob for Node.JS and Linux / Mac / Windows CLI (it's a one-liner)", "engines": {"node": ">= 4.5.0"}, "homepage": "https://git.coolaj86.com/coolaj86/atob.js.git", "keywords": ["atob", "browser"], "license": "(MIT OR Apache-2.0)", "main": "node-atob.js", "name": "atob", "repository": {"type": "git", "url": "git://git.coolaj86.com/coolaj86/atob.js.git"}, "version": "2.1.2"}