const path = require('path');
const fs = require('fs');
const db = require('../db/index');
const {
  IMAGE_TYPES,
  IMAGE_PATHS,
  createUploader,
  handleFileUpload,
  getImageFile,
  getImageById,
  getImagesByBusiness,
  deleteImage,
  ensureDirectoryExists
} = require('../utils/imageManager');

// 通用图片上传处理器
const uploadImage = (req, res) => {
  // 首先使用默认的头像类型创建uploader，稍后根据businessType调整
  const uploader = createUploader(IMAGE_TYPES.AVATAR, 1);

  uploader.single('image')(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    if (!req.file) {
      return res.output(new Error('请选择要上传的图片'), 400, null, false);
    }

    // 现在req.body应该已经被multer解析了
    const { businessType, businessId, operator } = req.body;

    // 手动验证必需参数
    if (!businessType) {
      return res.output(new Error('businessType is required'), 400, null, false);
    }

    if (!businessId) {
      return res.output(new Error('businessId is required'), 400, null, false);
    }

    if (!operator) {
      return res.output(new Error('operator is required'), 400, null, false);
    }

    // 验证业务类型
    const validBusinessTypes = ['customer', 'user', 'product', 'follow', 'component', 'order'];
    if (!validBusinessTypes.includes(businessType)) {
      return res.output(new Error(`businessType must be one of: ${validBusinessTypes.join(', ')}`), 400, null, false);
    }

    try {
      // 根据业务类型确定图片类型
      let imageType;
      switch (businessType) {
        case 'user':
          imageType = IMAGE_TYPES.AVATAR;
          break;
        case 'product':
          imageType = IMAGE_TYPES.PRODUCT;
          break;
        case 'follow':
          imageType = IMAGE_TYPES.FOLLOW;
          break;
        case 'customer':
          imageType = IMAGE_TYPES.CUSTOMER;
          break;
        case 'component':
          imageType = IMAGE_TYPES.COMPONENT;
          break;
        default:
          imageType = IMAGE_TYPES.AVATAR;
      }

      // 如果需要，移动文件到正确的目录
      if (imageType !== IMAGE_TYPES.AVATAR) {
        const oldPath = req.file.path;
        const newPath = path.join(IMAGE_PATHS[imageType], req.file.filename);

        // 确保目标目录存在
        ensureDirectoryExists(IMAGE_PATHS[imageType]);

        // 移动文件
        fs.renameSync(oldPath, newPath);
        req.file.path = newPath;
      }

      const savedImages = await handleFileUpload([req.file], imageType, {
        businessType: businessType,
        businessId: businessId,
        uploadUserId: req.user ? req.user.id : null,
        uploadUserName: operator
      });

      // 更新业务数据中的图片字段
      const imageUrl = savedImages[0].url;
      await updateBusinessImageField(businessType, businessId, imageUrl);

      res.output(null, 200, imageUrl, true);
    } catch (error) {
      console.error('通用图片上传失败:', error);
      res.output(error, 500, null, false);
    }
  });
};

// 上传头像图片
const uploadAvatar = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.AVATAR, 1);

  uploader.single('avatar')(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    if (!req.file) {
      return res.output(new Error('请选择要上传的图片'), 400, null, false);
    }

    try {
      const { businessType, businessId } = req.body;
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      const savedImages = await handleFileUpload([req.file], IMAGE_TYPES.AVATAR, {
        businessType: businessType || 'user',
        businessId: businessId,
        uploadUserId: uploadUserId,
        uploadUserName: uploadUserName
      });

      res.output(null, 200, {
        image: savedImages[0],
        message: '头像上传成功'
      }, true);
    } catch (error) {
      console.error('头像上传失败:', error);
      res.output(error, 500, null, false);
    }
  });
};

// 上传商品图片
const uploadProduct = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.PRODUCT, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    if (!req.files || req.files.length === 0) {
      return res.output(new Error('请选择要上传的图片'), 400, null, false);
    }

    try {
      const { businessType, businessId } = req.body;
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      const savedImages = await handleFileUpload(req.files, IMAGE_TYPES.PRODUCT, {
        businessType: businessType || 'product',
        businessId: businessId,
        uploadUserId: uploadUserId,
        uploadUserName: uploadUserName
      });

      res.output(null, 200, {
        images: savedImages,
        count: savedImages.length,
        message: '商品图片上传成功'
      }, true);
    } catch (error) {
      console.error('商品图片上传失败:', error);
      res.output(error, 500, null, false);
    }
  });
};

// 上传跟进图片
const uploadFollow = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.FOLLOW, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    if (!req.files || req.files.length === 0) {
      return res.output(new Error('请选择要上传的图片'), 400, null, false);
    }

    try {
      const { businessType, businessId } = req.body;
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      const savedImages = await handleFileUpload(req.files, IMAGE_TYPES.FOLLOW, {
        businessType: businessType || 'follow',
        businessId: businessId,
        uploadUserId: uploadUserId,
        uploadUserName: uploadUserName
      });

      res.output(null, 200, {
        images: savedImages,
        count: savedImages.length,
        message: '跟进图片上传成功'
      }, true);
    } catch (error) {
      console.error('跟进图片上传失败:', error);
      res.output(error, 500, null, false);
    }
  });
};

// 获取头像图片
const getAvatarImage = (req, res) => {
  try {
    const { fileName } = req.params;
    const imagePath = getImageFile(IMAGE_TYPES.AVATAR, fileName);

    if (imagePath) {
      res.sendFile(path.resolve(imagePath));
    } else {
      res.status(404).send('图片不存在');
    }
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取商品图片
const getProductImage = (req, res) => {
  try {
    const { fileName } = req.params;
    const imagePath = getImageFile(IMAGE_TYPES.PRODUCT, fileName);

    if (imagePath) {
      res.sendFile(path.resolve(imagePath));
    } else {
      res.status(404).send('图片不存在');
    }
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取跟进图片
const getFollowImage = (req, res) => {
  try {
    const { fileName } = req.params;
    const imagePath = getImageFile(IMAGE_TYPES.FOLLOW, fileName);

    if (imagePath) {
      res.sendFile(path.resolve(imagePath));
    } else {
      res.status(404).send('图片不存在');
    }
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取客户图片
const getCustomerImage = (req, res) => {
  try {
    const { fileName } = req.params;
    const imagePath = getImageFile(IMAGE_TYPES.CUSTOMER, fileName);

    if (imagePath) {
      res.sendFile(path.resolve(imagePath));
    } else {
      res.status(404).send('图片不存在');
    }
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

const getComponentImage= (req, res) => {
  try {
    const { fileName } = req.params;
    const imagePath = getImageFile(IMAGE_TYPES.COMPONENT, fileName);

    if (imagePath) {
      res.sendFile(path.resolve(imagePath));
    } else {
      res.status(404).send('图片不存在');
    }
  } catch (error) {
    res.output(error, 500, null, false);
  }
};


// 获取图片详情
const getImageDetail = async (req, res) => {
  try {
    const { imageId } = req.params;

    const imageInfo = await getImageById(imageId);

    if (!imageInfo) {
      return res.output(new Error('图片不存在'), 404, null, false);
    }

    res.output(null, 200, imageInfo, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取图片列表
const getImageList = async (req, res) => {
  try {
    const { imageType, businessType, businessId, page = 1, size = 10 } = req.query;

    let images = [];

    if (businessType && businessId) {
      images = await getImagesByBusiness(businessType, businessId);
    } else {
      // 这里可以添加更复杂的查询逻辑
      // 暂时返回模拟数据
      images = [];
    }

    // 简单的分页处理
    const startIndex = (page - 1) * size;
    const endIndex = startIndex + parseInt(size);
    const paginatedImages = images.slice(startIndex, endIndex);

    res.output(null, 200, {
      total: images.length,
      page: parseInt(page),
      size: parseInt(size),
      items: paginatedImages
    }, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 删除图片
const deleteImageHandler = async (req, res) => {
  try {
    const { imageId } = req.body;

    const imageInfo = await getImageById(imageId);

    if (!imageInfo) {
      return res.output(new Error('图片不存在'), 404, null, false);
    }

    await deleteImage(imageId);

    res.output(null, 200, { message: '图片删除成功' }, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 批量删除图片
const batchDeleteImages = async (req, res) => {
  try {
    const { imageIds } = req.body;

    if (!imageIds || !Array.isArray(imageIds) || imageIds.length === 0) {
      return res.output(new Error('请提供要删除的图片ID列表'), 400, null, false);
    }

    const results = [];

    for (const imageId of imageIds) {
      try {
        await deleteImage(imageId);
        results.push({ imageId, success: true });
      } catch (error) {
        results.push({ imageId, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;

    res.output(null, 200, {
      message: `批量删除完成，成功删除 ${successCount}/${imageIds.length} 张图片`,
      results: results
    }, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 更新业务数据中的图片字段
async function updateBusinessImageField(businessType, businessId, imageUrl) {
  return new Promise((resolve, reject) => {
    let sql;
    let tableName;
    let imageField;

    switch (businessType) {
      case 'user':
        tableName = 'users';
        imageField = 'avatarUrl';
        sql = `UPDATE ${tableName} SET ${imageField} = ? WHERE id = ?`;
        break;
      case 'customer':
        tableName = 'customers';
        imageField = 'image';
        sql = `UPDATE ${tableName} SET ${imageField} = ? WHERE id = ?`;
        break;
      case 'product':
        tableName = 'products';
        imageField = 'image';
        sql = `UPDATE ${tableName} SET ${imageField} = ? WHERE id = ?`;
        break;
      case 'component':
        tableName = 'components';
        imageField = 'image';
        sql = `UPDATE ${tableName} SET ${imageField} = ? WHERE id = ?`;
        break;
      default:
        // 对于其他业务类型，不更新数据库字段，只保存图片记录
        return resolve();
    }

    db.query(sql, [imageUrl, businessId], (err, results) => {
      if (err) {
        console.error(`更新${businessType}图片字段失败:`, err);
        return reject(err);
      }

      if (results.affectedRows === 0) {
        console.warn(`未找到要更新的${businessType}记录: ${businessId}`);
      }

      resolve(results);
    });
  });
}

module.exports = {
  uploadImage,

  uploadAvatar,
  uploadProduct,
  uploadFollow,
  getAvatarImage,
  getProductImage,
  getFollowImage,
  getCustomerImage,
  getComponentImage,
  getImageDetail,
  getImageList,
  deleteImage: deleteImageHandler,
  batchDeleteImages
};
