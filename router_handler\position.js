const db = require('../db/index')
const log = require('./log')
// 获取职位列表
const getPositionList = (req, res) => {
  try {
    const user = req.user;
    const { keyword = '', page = 1, size = 10 } = req.query;
    console.log("查询参数:", { page, size, keyword });

    // 构建SQL查询
    let extSql = "";
    let params = [];

    if (keyword && keyword.trim() !== '') {
      extSql = "AND (`name` LIKE ? OR 'code' LIKE ?) ";
      const searchTerm = `%${keyword}%`;
      params = [searchTerm,searchTerm];
    }

    // 计算分页
    const offset = (page - 1) * size;

    // 查询总数
    const countSql = "SELECT COUNT(*) as total FROM positions WHERE  `status` = 1 " + extSql;
    db.query(countSql, params, (countErr, countResults) => {
      if (countErr) {
        console.error("查询总数出错:", countErr);
        return res.output(countErr, 500);
      }

      const total = countResults[0].total;

      // 查询数据
      const dataSql = "SELECT * FROM positions WHERE `status` = 1 " + extSql + " LIMIT ?, ?";
      const dataParams = [...params, offset, size];
      db.query(dataSql, dataParams, (err, results) => {
        if (err) {
          console.error("查询数据出错:", err);
          return res.output(err, 500);
        }

        // 返回结果
        res.output(null, 200, {
          total: total,
          page: page,
          size: size,
          list: results
        }, true);
      });
    });    
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取职位详情
const getPositionDetail = (req, res) => {
  try {
    const { id } = req.query;
    const sql = 'select * from positions where id= ?'
    db.query(sql, id, (err, results) => {
      if (err) return res.output(err)
      if (results.length !== 1) return res.output(new Error("获取职务信息失败！"), 400)
      res.output(null, 200, results[0], true);
    })
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 新增职位
const addPosition = (req, res) => {
  try {
    const user = req.user;
    const { name, code } = req.body;

    const sqlStr = "insert into positions set ?";
    db.query(sqlStr, { name, code }, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("添加职位失败！"), 400);
      res.output(null, 200, { id: results.insertId,message: "添加职位成功" }, true);
    });
    log.add("sysLog", "添加职位:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 更新职位
const updatePosition = (req, res) => {
  try {
    const user = req.user;
    const { id, name, code, status } = req.body;
    const sqlStr = 'update positions set name=?,status=? where id =?'
    db.query(sqlStr, [name, status, id], (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('更新职务信息失败！'), 400)
      res.output(null, 200, { id: id, name: name, code: code, status: status }, true)
    })
    log.add("sysLog", "修改职务信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 删除职位
const deletePosition = (req, res) => {
  try {
    const user = req.user;
    const id = req.body.id;
    const sql = 'update positions set delFlag=1 where id=?';
    db.query(sql, id, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("删除职位失败！"), 400);
      res.output(null, 200, { message: "删除职位成功" }, true);
    });
    log.add("sysLog", "删除职位:" + id, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// Android应用专用：获取职位下拉框数据
const getPositions = (req, res) => {
  try {
    // 查询数据
    const dataSql = "SELECT * FROM positions WHERE delFlag=0 ";
    db.query(dataSql, null, (err, results) => {
      if (err) {
        console.error("查询数据出错:", err);
        return res.output(err, 500);
      }
      // 返回结果
      res.output(null, 200, results, true);
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

module.exports = {
  getPositionList,
  getPositionDetail,
  addPosition,
  updatePosition,
  deletePosition,
  getPositions
};
