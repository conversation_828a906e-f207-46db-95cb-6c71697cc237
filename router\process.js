const express = require('express');
const router = express.Router();
const process_handler = require('../router_handler/process');
const expressJoi = require('@escook/express-joi');
const { 
  process_list_schema, 
  process_detail_schema, 
  process_add_schema, 
  process_update_schema, 
  process_delete_schema
} = require('../schema/process');

// 获取流程节点列表
router.get('/list', expressJoi(process_list_schema), process_handler.getProcessList);

// 获取流程节点详情
router.get('/detail', expressJoi(process_detail_schema), process_handler.getProcessDetail);

// 新增流程节点
router.post('/add', expressJoi(process_add_schema), process_handler.addProcess);

// 更新流程节点
router.post('/update', expressJoi(process_update_schema), process_handler.updateProcess);

// 删除流程节点
router.post('/delete', expressJoi(process_delete_schema), process_handler.deleteProcess);

// Android应用专用：获取流程节点下拉框数据
router.get('/', process_handler.getProcesses);
module.exports = router;
