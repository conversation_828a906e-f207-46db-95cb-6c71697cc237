const express = require('express');
const router = express.Router();
const image_handler = require('../router_handler/image');
const expressJoi = require('@escook/express-joi');
const {
  image_upload_schema,
  general_image_upload_schema,
  multiple_image_upload_schema,
  image_detail_schema,
  image_list_schema,
  image_delete_schema,  
  image_delete_by_url_schema,
  business_images_schema
} = require('../schema/image');
// 通用图片上传接口
router.post('/uploadImage', image_handler.uploadImage);

// 批量图片上传接口
router.post('/uploadMultipleImages', image_handler.uploadMultipleImages);

// 获取图片文件 - 头像
router.get('/avatar/:fileName', image_handler.getAvatarImage);

// 获取图片文件 - 商品
router.get('/product/:fileName', image_handler.getProductImage);

// 获取图片文件 - 跟进
router.get('/follow/:fileName', image_handler.getFollowImage);

// 获取图片文件 - 客户
router.get('/customer/:fileName', image_handler.getCustomerImage);

// 根据ID获取图片信息
router.get('/detail/:imageId', expressJoi(image_detail_schema), image_handler.getImageDetail);

// 获取业务相关的图片列表
router.get('/list', expressJoi(image_list_schema), image_handler.getImageList);

// 删除图片
router.post('/delete', expressJoi(image_delete_schema), image_handler.deleteImage);

// 批量删除图片
router.post('/batch-delete', image_handler.batchDeleteImages);

// 删除图片接口 (通过URL)
router.post('/deleteImage', expressJoi(image_delete_by_url_schema), image_handler.deleteImageByUrl);

// 获取业务对象的所有图片
router.get('/getImages', expressJoi(business_images_schema), image_handler.getBusinessImages);

module.exports = router;
