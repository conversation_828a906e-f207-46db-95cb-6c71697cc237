{"_from": "arr-union@^3.1.0", "_id": "arr-union@3.1.0", "_inBundle": false, "_integrity": "sha512-sKpyeERZ02v1FeCZT8lrfJq5u6goHCtpTAzPwJYe7c8SPFOboNjNg1vz2L4VTn9T4PQxEx13TbXLmYUcS6Ug7Q==", "_location": "/arr-union", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "arr-union@^3.1.0", "name": "arr-union", "escapedName": "arr-union", "rawSpec": "^3.1.0", "saveSpec": null, "fetchSpec": "^3.1.0"}, "_requiredBy": ["/class-utils", "/union-value"], "_resolved": "https://registry.npmjs.org/arr-union/-/arr-union-3.1.0.tgz", "_shasum": "e39b09aea9def866a8f206e288af63919bae39c4", "_spec": "arr-union@^3.1.0", "_where": "D:\\Code\\node-express\\node_modules\\union-value", "author": {"name": "<PERSON>", "url": "https://github.com/jonschlinkert"}, "bugs": {"url": "https://github.com/jonschlinkert/arr-union/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Combines a list of arrays, returning a single array with unique values, using strict equality for comparisons.", "devDependencies": {"ansi-bold": "^0.1.1", "array-union": "^1.0.1", "array-unique": "^0.2.1", "benchmarked": "^0.1.4", "gulp-format-md": "^0.1.7", "minimist": "^1.1.1", "mocha": "*", "should": "*"}, "engines": {"node": ">=0.10.0"}, "files": ["index.js"], "homepage": "https://github.com/jonschlinkert/arr-union", "keywords": ["add", "append", "array", "arrays", "combine", "concat", "extend", "union", "uniq", "unique", "util", "utility", "utils"], "license": "MIT", "main": "index.js", "name": "arr-union", "repository": {"type": "git", "url": "git+https://github.com/jonschlinkert/arr-union.git"}, "scripts": {"test": "mocha"}, "verb": {"run": true, "toc": false, "layout": "default", "tasks": ["readme"], "plugins": ["gulp-format-md"], "related": {"list": ["arr-diff", "arr-flatten", "arr-filter", "arr-map", "arr-pluck", "arr-reduce", "array-unique"]}, "reflinks": ["verb", "array-union"], "lint": {"reflinks": true}}, "version": "3.1.0"}