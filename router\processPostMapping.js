const express = require('express');
const router = express.Router();
const mapping_handler = require('../router_handler/mapping');
const expressJoi = require('@escook/express-joi');
const {
    mapping_list_schema,
    mapping_detail_schema,
    mapping_add_schema,
    mapping_update_schema,
    mapping_delete_schema
} = require('../schema/mapping');

router.get('/list', expressJoi(mapping_list_schema), mapping_handler.getMappingList);
router.get('/detail', expressJoi(mapping_detail_schema), mapping_handler.getMappingDetail);
router.post('/add', expressJoi(mapping_add_schema), mapping_handler.addMapping);
router.post('/update', expressJoi(mapping_update_schema), mapping_handler.updateMapping);
router.post('/delete', expressJoi(mapping_delete_schema), mapping_handler.deleteMapping);

module.exports = router;