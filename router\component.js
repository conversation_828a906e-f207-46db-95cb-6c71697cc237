const express = require('express');
const router = express.Router();
const expressJoi = require('@escook/express-joi');
const component_handler = require('../router_handler/component');

const {
    component_list_schema,
    component_detail_schema,
    component_add_schema,
    component_update_schema,
    component_delete_schema
} = require('../schema/component');

// 获取所有
router.get('/list', express<PERSON>oi(component_list_schema), component_handler.getComponentList);
// 获取详情
router.get('/detail', expressJoi(component_detail_schema), component_handler.getComponentDetail);

// 新增
router.post('/add', expressJoi(component_add_schema), component_handler.addComponent);

// 更新
router.post('/update', expressJoi(component_update_schema), component_handler.updateComponent);

// 删除(伪删除)
router.post('/delete', expressJoi(component_delete_schema), component_handler.deleteComponent);

module.exports = router;
