const db = require('../db/index')
const log = require('./log')

const getMappingList = (req, res) => {
    try {
        const user = req.user;
        const { keyword = '', page = 1, size = 10 } = req.query;
        console.log("查询参数:", { page, size, keyword });

        let extSql = "";
        let params = [];

        if (keyword && keyword.trim() !== '') {
            extSql = "AND (ps.`name` LIKE ? OR ps.'code' LIKE ? OR pe.`name` LIKE ? OR pe.`code` LIKE ?) ";
            const searchTerm = `%${keyword}%`;
            params = [searchTerm, searchTerm, searchTerm, searchTerm];
        }

        // 计算分页
        const offset = (page - 1) * size;

        // 查询总数
        const countSql = "SELECT COUNT(*) as total FROM followassociation fa WHERE  fa.`delFlag` = 0 " + extSql;
        db.query(countSql, params, (countErr, countResults) => {
            if (countErr) {
                console.error("查询总数出错:", countErr);
                return res.output(countErr, 500);
            }

            const total = countResults[0].total;

            // 查询数据
            const dataSql = "SELECT	fa.id,fa.status,fa.remark,"
                + "ps.id AS postId,ps.`code` AS postCode,ps.`name` AS postName,ps.`status` AS postStatus,"
                + "pe.id AS processId,pe.`code` AS processCode,pe.`name` AS processName,pe.`status` AS processStatus "
                + "FROM	followassociation fa "
                + "LEFT JOIN posts ps ON fa.jobId = ps.id "
                + "LEFT JOIN processes pe ON fa.processId = pe.id WHERE	fa.`delFlag` = 0 "
                + extSql + " LIMIT ?, ?";
            const dataParams = [...params, offset, size];
            db.query(dataSql, dataParams, (err, results) => {
                if (err) {
                    console.error("查询数据出错:", err);
                    return res.output(err, 500);
                }

                // 返回结果
                res.output(null, 200, {
                    total: total,
                    page: page,
                    size: size,
                    list: results
                }, true);
            });
        });
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const getMappingDetail = (req, res) => {
    try {
        const { id } = req.query;
        const sql = "SELECT	fa.id,fa.status,fa.remark,"
            + "ps.id AS postId,ps.`code` AS postCode,ps.`name` AS postName,ps.`status` AS postStatus,"
            + "pe.id AS processId,pe.`code` AS processCode,pe.`name` AS processName,pe.`status` AS processStatus "
            + "FROM	followassociation fa "
            + "LEFT JOIN posts ps ON fa.jobId = ps.id "
            + "LEFT JOIN processes pe ON fa.processId = pe.id WHERE	fa.`delFlag` = 0 "
            + "where id= ?"
        db.query(sql, id, (err, results) => {
            if (err) return res.output(err)
            if (results.length !== 1) return res.output(new Error("获取流程岗位映射信息失败！"), 400)
            res.output(null, 200, results[0], true);
        })
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const addMapping = (req, res) => {
    try {
        const { processId, postId, status, remark } = req.body;
        const jobId = postId;
        const sqlStr = "insert into followassociation set ?";
        db.query(sqlStr, { processId, jobId, status, remark }, (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error("新增流程岗位映射失败！"), 400);
            res.output(null, 200, { id: results.insertId, message: "新增流程岗位映射成功" });
        });
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const updateMapping = (req, res) => {
    try {
        const { id, processId, postId, status, remark } = req.body;
        const jobId = postId;
        const sqlStr = "update followassociation set processId=?,jobId=?,status=?,remark=? where id=?";
        db.query(sqlStr, [processId, jobId, status, remark, id], (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error('更新流程岗位映射失败！'), 400)
            res.output(null, 200, { id: id, status: status }, true)
        });
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

const deleteMapping = (req, res) => {
    try {
        const { id } = req.body;
        const sqlStr = "update followassociation set delFlag=1 where id=?";
        db.query(sqlStr, id, (err, results) => {
            if (err) return res.output(err);
            if (results.affectedRows !== 1) return res.output(new Error('删除流程岗位映射失败！'), 400)
            res.output(null, 200, { id: id }, true)
        });
    } catch (error) {
        res.output(error, 500, null, false);
    }
}

module.exports = {
    getMappingList,
    getMappingDetail,
    addMapping,
    updateMapping,
    deleteMapping
};

