const express = require('express');
const router = express.Router();
const position_handler = require('../router_handler/position');
const expressJoi = require('@escook/express-joi');
const {
  position_list_schema,
  position_detail_schema,
  position_add_schema,
  position_update_schema,
  position_delete_schema
} = require('../schema/position');

// 获取职位列表
router.get('/list', express<PERSON><PERSON>(position_list_schema), position_handler.getPositionList);

// 获取职位详情
router.get('/detail', expressJoi(position_detail_schema), position_handler.getPositionDetail);

// 新增职位
router.post('/add', express<PERSON>oi(position_add_schema), position_handler.addPosition);

// 更新职位
router.post('/update', expressJoi(position_update_schema), position_handler.updatePosition);

// 删除职位(伪删除)
router.post('/delete', expressJoi(position_delete_schema), position_handler.deletePosition);

// Android应用专用：获取职位下拉框数据
router.get('/', position_handler.getPositions);

module.exports = router;
