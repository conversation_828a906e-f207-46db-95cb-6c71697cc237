{"_from": "dunder-proto@^1.0.1", "_id": "dunder-proto@1.0.1", "_inBundle": false, "_integrity": "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A==", "_location": "/dunder-proto", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "dunder-proto@^1.0.1", "name": "dunder-proto", "escapedName": "dunder-proto", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/get-proto"], "_resolved": "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz", "_shasum": "d7ae667e1dc83482f8b70fd0f6eefc50da30f58a", "_spec": "dunder-proto@^1.0.1", "_where": "D:\\Code\\node-express\\node_modules\\get-proto", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/es-shims/dunder-proto/issues"}, "bundleDependencies": false, "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "deprecated": false, "description": "If available, the `Object.prototype.__proto__` accessor and mutator, call-bound", "devDependencies": {"@arethetypeswrong/cli": "^0.17.1", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/tape": "^5.7.0", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "eslint": "=8.8.0", "evalmd": "^0.0.19", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {"./get": "./get.js", "./set": "./set.js", "./package.json": "./package.json"}, "homepage": "https://github.com/es-shims/dunder-proto#readme", "license": "MIT", "main": false, "name": "dunder-proto", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/es-shims/dunder-proto.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>= 10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=autogenerated", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "sideEffects": false, "testling": {"files": "test/index.js"}, "version": "1.0.1"}