const path = require('path');
const fs = require('fs');
const { v4: uuidv4 } = require('uuid');
const {
  IMAGE_TYPES,
  createUploader,
  handleFileUpload,
  getImageFile
} = require('../utils/imageManager');

// 获取跟进记录列表
const getFollowList = (req, res) => {
  try {
    const { businessType, businessId, page = 1, size = 10 } = req.query;

    // 模拟数据
    const mockData = {
      total: 8,
      items: [
        {
          id: "f8c7d9e6-5a4b-3c2d-1e0f-9a8b7c6d5e4f",
          businessType: businessType,
          businessId: businessId,
          content: "已完成初步设计",
          status: "进行中",
          imageUrls: [
            "/api/follow/image/img_20230601_001.jpg",
            "/api/follow/image/img_20230601_002.jpg"
          ],
          createTime: "2023-06-01 14:30:00",
          createUser: "张三",
          createUserId: "u1v2w3x4-y5z6-7a8b-9c0d-e1f2g3h4i5j6"
        }
      ]
    };

    res.output(null, 200, mockData, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取跟进记录详情
const getFollowDetail = (req, res) => {
  try {
    const { id } = req.query;

    // 模拟数据
    const mockData = {
      id: id,
      businessType: "product",
      businessId: "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
      businessName: "产品A",
      content: "已完成初步设计",
      status: "进行中",
      remark: "设计文档已上传至共享网盘",
      images: [
        {
          id: "i1j2k3l4-m5n6-7o8p-9q0r-s1t2u3v4w5x6",
          url: "/api/follow/image/img_20230601_001.jpg",
          name: "设计草图",
          base64Data: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
          uploadTime: "2023-06-01 14:28:00"
        }
      ],
      createTime: "2023-06-01 14:30:00",
      createUser: "张三",
      createUserId: "u1v2w3x4-y5z6-7a8b-9c0d-e1f2g3h4i5j6",
      updateTime: "2023-06-01 16:45:00",
      updateUser: "张三",
      updateUserId: "u1v2w3x4-y5z6-7a8b-9c0d-e1f2g3h4i5j6"
    };

    res.output(null, 200, mockData, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 添加跟进记录
const addFollow = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.FOLLOW, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    try {
      const { businessType, businessId, content, status, remark } = req.body;
      const files = req.files || [];
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      // 模拟新增操作
      const newId = uuidv4();

      let savedImages = [];
      if (files.length > 0) {
        savedImages = await handleFileUpload(files, IMAGE_TYPES.FOLLOW, {
          businessType: businessType,
          businessId: newId, // 使用跟进记录ID作为businessId
          uploadUserId: uploadUserId,
          uploadUserName: uploadUserName
        });
      }

      const imageUrls = savedImages.map(img => img.url);

      res.output(null, 200, {
        id: newId,
        imageUrls: imageUrls,
        images: savedImages
      }, true);
    } catch (error) {
      res.output(error, 500, null, false);
    }
  });
};

// 更新跟进记录
const updateFollow = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.FOLLOW, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    try {
      const { id, content, status, remark, deleteImageIds } = req.body;
      const files = req.files || [];
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      let savedImages = [];
      if (files.length > 0) {
        savedImages = await handleFileUpload(files, IMAGE_TYPES.FOLLOW, {
          businessType: 'follow',
          businessId: id,
          uploadUserId: uploadUserId,
          uploadUserName: uploadUserName
        });
      }

      // 处理删除的图片
      if (deleteImageIds && Array.isArray(deleteImageIds)) {
        const { deleteImage } = require('../utils/imageManager');
        for (const imageId of deleteImageIds) {
          try {
            await deleteImage(imageId);
          } catch (error) {
            console.error('删除图片失败:', imageId, error);
          }
        }
      }

      const imageUrls = savedImages.map(img => img.url);

      res.output(null, 200, {
        id,
        imageUrls: imageUrls,
        newImages: savedImages,
        deletedImageIds: deleteImageIds || []
      }, true);
    } catch (error) {
      res.output(error, 500, null, false);
    }
  });
};

// 删除跟进记录
const deleteFollow = (req, res) => {
  try {
    const { id } = req.body;

    // 模拟删除操作
    res.output(null, 200, null, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取图片
const getImage = (req, res) => {
  try {
    const { imageName } = req.params;
    const imagePath = getImageFile(IMAGE_TYPES.FOLLOW, imageName);

    if (imagePath) {
      res.sendFile(path.resolve(imagePath));
    } else {
      res.status(404).send('图片不存在');
    }
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 批量上传图片
const uploadImages = (req, res) => {
  const uploader = createUploader(IMAGE_TYPES.FOLLOW, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    try {
      const { businessType, businessId } = req.body;
      const files = req.files || [];
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      let savedImages = [];
      if (files.length > 0) {
        savedImages = await handleFileUpload(files, IMAGE_TYPES.FOLLOW, {
          businessType: businessType,
          businessId: businessId,
          uploadUserId: uploadUserId,
          uploadUserName: uploadUserName
        });
      }

      const imageIds = savedImages.map(img => img.id);
      const imageUrls = savedImages.map(img => img.url);

      res.output(null, 200, {
        imageIds,
        imageUrls,
        images: savedImages
      }, true);
    } catch (error) {
      res.output(error, 500, null, false);
    }
  });
};

// 获取业务对象的所有跟进记录
const getBusinessFollows = (req, res) => {
  try {
    const { businessType, businessId, page = 1, size = 10 } = req.query;

    // 模拟数据
    const mockData = {
      total: 5,
      items: [
        {
          id: "f8c7d9e6-5a4b-3c2d-1e0f-9a8b7c6d5e4f",
          content: "已完成初步设计",
          status: "进行中",
          imageUrls: [
            "/api/follow/image/img_20230601_001.jpg",
            "/api/follow/image/img_20230601_002.jpg"
          ],
          createTime: "2023-06-01 14:30:00",
          createUser: "张三"
        },
        {
          id: "a1b2c3d4-e5f6-7g8h-9i0j-k1l2m3n4o5p6",
          content: "已完成详细设计",
          status: "已完成",
          imageUrls: [
            "/api/follow/image/img_20230605_001.jpg"
          ],
          createTime: "2023-06-05 10:15:00",
          createUser: "李四"
        }
      ],
      businessInfo: {
        id: businessId,
        type: businessType,
        name: "产品A",
        code: "PA001"
      }
    };

    res.output(null, 200, mockData, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取图片的Base64编码
const getImageBase64 = (req, res) => {
  try {
    const { imageId } = req.params;

    // 模拟数据
    const mockData = {
      id: imageId,
      name: "设计草图",
      base64Data: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD..."
    };

    res.output(null, 200, mockData, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

module.exports = {
  getFollowList,
  getFollowDetail,
  addFollow,
  updateFollow,
  deleteFollow,
  getImage,
  uploadImages,
  getBusinessFollows,
  getImageBase64
};
