const joi = require('joi');

const component_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

const component_detail_schema = {
  query: {
    id: joi.number().integer().required()
  }
};

const component_add_schema = {
  body: {
    code: joi.string().required(),
    name: joi.string().required(),
    model: joi.string().required(),
    standard: joi.string().required(),
    remark: joi.string().allow('')
  }
};

const component_update_schema = {
  body: {
    id: joi.number().integer().required(),
    code: joi.string().required(),
    name: joi.string().required(),
    model: joi.string().required(),
    standard: joi.string().required(),
    remark: joi.string().allow('')
  }
};

const component_delete_schema = {
  body: {
    id: joi.number().integer().required()
  }
};

module.exports = {
  component_list_schema,
  component_detail_schema,
  component_add_schema,
  component_update_schema,
  component_delete_schema
};
