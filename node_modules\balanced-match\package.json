{"_from": "balanced-match@^1.0.0", "_id": "balanced-match@1.0.2", "_inBundle": false, "_integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==", "_location": "/balanced-match", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "balanced-match@^1.0.0", "name": "balanced-match", "escapedName": "balanced-match", "rawSpec": "^1.0.0", "saveSpec": null, "fetchSpec": "^1.0.0"}, "_requiredBy": ["/brace-expansion"], "_resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "_shasum": "e83e3a7e3f300b34cb9d87f615fa0cbf357690ee", "_spec": "balanced-match@^1.0.0", "_where": "D:\\Code\\node-express\\node_modules\\brace-expansion", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "bugs": {"url": "https://github.com/juliangruber/balanced-match/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Match balanced character pairs, like \"{\" and \"}\"", "devDependencies": {"matcha": "^0.7.0", "tape": "^4.6.0"}, "homepage": "https://github.com/juliangruber/balanced-match", "keywords": ["match", "regexp", "test", "balanced", "parse"], "license": "MIT", "main": "index.js", "name": "balanced-match", "repository": {"type": "git", "url": "git://github.com/juliangruber/balanced-match.git"}, "scripts": {"bench": "matcha test/bench.js", "test": "tape test/test.js"}, "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/20..latest", "firefox/nightly", "chrome/25..latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "version": "1.0.2"}