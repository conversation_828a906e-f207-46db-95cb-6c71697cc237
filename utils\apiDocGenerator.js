const apiRoutes = {
  // 认证相关
  '/auth/login': '用户登录',
  '/auth/register': '用户基本注册',
  '/auth/checkUsername': '检查重复用户名',
  '/auth/registerWithAvatar': '用户注册并上传图像',

  //个人信息管理
  '/api/user/profile': '获取已登录用户信息',
  '/api/user/avatar': '获取已登录用户头像',
  '/api/user/updateProfile': '修改个人用户信息',
  '/api/user/changePassword': '修改个人用户密码',
  '/api/user/updateAvatar': '更新个人用户图像',

  // 用户管理
  '/api/user/all': '获取所有用户信息',
  '/api/user/findByName': '获取用户信息',
  '/api/user/updateUser': '修改用户信息',
  '/api/user/delete': '删除用户信息',

  //用户审核
  '/api/user/pendingList': '获取待审用户列表',
  '/api/user/pendingUser': '获取待审用户信息',
  '/api/user/auditUser': '提交审核用户信息',
  '/api/user/detail': '获取审核用户信息',

  // 部门管理
  '/api/department/list': '获取部门列表',
  '/api/department/detail': '获取部门详情',
  '/api/department/add': '新增部门',
  '/api/department/update': '更新部门',
  '/api/department/delete': '删除部门',
  '/api/department': '获取部门下拉框数据(Android专用)',

  // 职位管理
  '/api/position/list': '获取职位列表',
  '/api/position/detail': '获取职位详情',
  '/api/position/add': '新增职位',
  '/api/position/update': '更新职位',
  '/api/position/delete': '删除职位',
  '/api/position': '获取职位下拉框数据(Android专用)',

  // 岗位管理
  '/api/job/list': '获取岗位列表',
  '/api/job/detail': '获取岗位详情',
  '/api/job/add': '新增岗位',
  '/api/job/update': '更新岗位',
  '/api/job/delete': '删除岗位',
  '/api/job': '获取岗位下拉框数据(Android专用)',

  // 流程管理
  '/api/process/list': '获取流程节点列表',
  '/api/process/detail': '获取流程节点详情',
  '/api/process/add': '新增流程节点',
  '/api/process/update': '更新流程节点',

  // 权限管理
  '/api/permission/list': '获取权限模板列表',
  '/api/permission/detail': '获取权限模板详情',
  '/api/permission/add': '新增权限模板',
  '/api/permission/update': '更新权限模板',
  '/api/module/list': '获取所有业务模块',

  // 待办事项管理
  '/api/todo/list': '获取待办事项列表',
  '/api/todo/detail': '获取订单流程节点跟进详情',
  '/api/todo/addRecord': '添加跟进记录',
  '/api/todo/updateStatus': '更新跟进状态',

  // 商品管理
  '/api/product/list': '获取商品列表',
  '/api/product/detail': '获取商品详情',
  '/api/product/add': '新增商品',
  '/api/product/update': '更新商品',
  '/api/product/delete': '删除商品',

  // 部件管理
  '/api/component/list': '获取部件列表',
  '/api/component/detail': '获取部件详情',
  '/api/component/add': '新增部件',
  '/api/component/update': '更新部件',
  '/api/component/delete': '删除部件',

  // 跟进管理
  '/api/follow/list': '获取跟进记录列表',
  '/api/follow/detail': '获取跟进记录详情',
  '/api/follow/add': '添加跟进记录',
  '/api/follow/update': '更新跟进记录',
  '/api/follow/delete': '删除跟进记录',
  '/api/follow/image/:imageName': '获取图片',
  '/api/follow/uploadImages': '批量上传图片',
  '/api/follow/business': '获取业务对象的所有跟进记录',
  '/api/follow/imageBase64/:imageId': '获取图片的Base64编码',

  // 图片管理
  '/api/picture/upload': '上传图片',
  '/api/picture/download': '从远程HTTP服务器请求图片路径',
  '/api/picture/base64Img': '根据imgName请求Base64图片列表',
  '/api/picture/imgList': '根据fileType和businessId请求Base64图片列表',

  // 序列号管理
  '/api/serialNumber/serialNumber': '获取序列号',
  '/api/serialNumber/serialNumberByCode': '根据二维码获取序列号',

  // 图片管理
  '/api/image/upload/avatar': '上传头像图片',
  '/api/image/upload/product': '上传商品图片',
  '/api/image/upload/follow': '上传跟进图片',
  '/api/image/uploadImage': '通用图片上传接口',
  '/api/image/uploadMultipleImages': '批量图片上传接口',
  '/api/image/avatar/:fileName': '获取头像图片',
  '/api/image/product/:fileName': '获取商品图片',
  '/api/image/follow/:fileName': '获取跟进图片',
  '/api/image/customer/:fileName': '获取客户图片',
  '/api/image/component/:fileName': '获取部件图片',
  '/api/image/detail/:imageId': '获取图片详情',
  '/api/image/list': '获取图片列表',
  '/api/image/delete': '删除图片',
  '/api/image/deleteImage': '删除图片接口 (通过URL)',
  '/api/image/getImages': '获取业务对象的所有图片',
  '/api/image/batch-delete': '批量删除图片',

  // Android专用API (不带/api前缀)
  '/departments': '获取部门下拉框数据(Android)',
  '/positions': '获取职位下拉框数据(Android)',
  '/posts': '获取岗位下拉框数据(Android)',
  '/permissions': '获取权限模板下拉框数据(Android)'
};

// 生成详细的API文档
function generateDetailedApiDocs(routes, apiSpec) {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>企业管理系统 API 文档</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
            align-items: start;
        }

        .sidebar {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            padding: 25px;
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        .sidebar h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.2em;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 25px;
        }

        .stat-item {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }

        .stat-number {
            font-size: 1.8em;
            font-weight: bold;
            display: block;
        }

        .stat-label {
            font-size: 0.8em;
            opacity: 0.9;
        }

        .nav-section {
            margin-bottom: 20px;
        }

        .nav-section h4 {
            color: #555;
            margin-bottom: 10px;
            font-size: 0.9em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .nav-item {
            display: block;
            padding: 8px 12px;
            color: #666;
            text-decoration: none;
            border-radius: 6px;
            margin-bottom: 4px;
            transition: all 0.2s ease;
            font-size: 0.9em;
        }

        .nav-item:hover {
            background: #f8f9fa;
            color: #007bff;
            transform: translateX(4px);
        }

        .nav-item.active {
            background: #007bff;
            color: white;
        }

        .content-area {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .section-group {
            border-bottom: 1px solid #f0f0f0;
        }

        .section-group:last-child {
            border-bottom: none;
        }

        .section-header {
            background: #f8f9fa;
            padding: 20px 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
            border-left: 4px solid transparent;
        }

        .section-header:hover {
            background: #e9ecef;
            border-left-color: #007bff;
        }

        .section-header.active {
            background: #007bff;
            color: white;
            border-left-color: #0056b3;
        }

        .section-title {
            display: flex;
            align-items: center;
            font-size: 1.1em;
            font-weight: 600;
        }

        .section-icon {
            margin-right: 10px;
            width: 20px;
        }

        .section-count {
            background: rgba(0,0,0,0.1);
            color: inherit;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
        }

        .section-header.active .section-count {
            background: rgba(255,255,255,0.2);
        }

        .toggle-icon {
            transition: transform 0.2s ease;
            font-size: 0.9em;
        }

        .section-header.active .toggle-icon {
            transform: rotate(180deg);
        }

        .section-content {
            display: none;
            padding: 0;
        }

        .section-content.active {
            display: block;
        }

        .api-item {
            border-bottom: 1px solid #f0f0f0;
            transition: all 0.2s ease;
        }

        .api-item:last-child {
            border-bottom: none;
        }

        .api-header {
            padding: 15px 25px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: space-between;
            transition: all 0.2s ease;
        }

        .api-header:hover {
            background: #f8f9fa;
        }

        .api-header.active {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .api-basic-info {
            display: flex;
            align-items: center;
            flex: 1;
        }

        .method-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-size: 0.75em;
            margin-right: 12px;
            min-width: 50px;
            text-align: center;
            text-transform: uppercase;
        }

        .method-get { background: #4caf50; color: white; }
        .method-post { background: #2196f3; color: white; }
        .method-put { background: #ff9800; color: white; }
        .method-delete { background: #f44336; color: white; }

        .api-path {
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-weight: 500;
            color: #333;
            margin-right: 15px;
            flex: 1;
        }

        .api-description {
            color: #666;
            font-size: 0.9em;
            margin-right: 15px;
        }

        .auth-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 0.7em;
            font-weight: bold;
            margin-right: 10px;
        }

        .auth-required { background: #ffebee; color: #c62828; }
        .auth-optional { background: #e8f5e8; color: #2e7d32; }

        .api-details {
            display: none;
            padding: 25px;
            background: #fafafa;
            border-top: 1px solid #e0e0e0;
        }

        .api-details.active {
            display: block;
        }

        .detail-section {
            margin-bottom: 25px;
        }

        .detail-section:last-child {
            margin-bottom: 0;
        }

        .detail-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
        }

        .detail-title i {
            margin-right: 8px;
            color: #007bff;
        }

        .param-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 6px;
            overflow: hidden;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .param-table th {
            background: #f5f5f5;
            padding: 12px;
            text-align: left;
            font-weight: 600;
            color: #555;
            border-bottom: 1px solid #ddd;
        }

        .param-table td {
            padding: 12px;
            border-bottom: 1px solid #f0f0f0;
        }

        .param-table tr:last-child td {
            border-bottom: none;
        }

        .required { color: #f44336; font-weight: 600; }
        .optional { color: #4caf50; }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 6px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.85em;
            overflow-x: auto;
            position: relative;
        }

        .code-block::before {
            content: attr(data-lang);
            position: absolute;
            top: 8px;
            right: 12px;
            background: rgba(255,255,255,0.1);
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.7em;
            text-transform: uppercase;
        }

        .response-example {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }

        .response-header {
            background: #f5f5f5;
            padding: 10px 15px;
            font-weight: 600;
            color: #555;
            border-bottom: 1px solid #e0e0e0;
        }

        .response-body {
            padding: 15px;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .sidebar {
                position: static;
                max-height: none;
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }
        }

        .search-box {
            width: 100%;
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 6px;
            margin-bottom: 20px;
            font-size: 0.9em;
        }

        .search-box:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-code"></i> 企业管理系统 API 文档</h1>
            <p>现代化、交互式的API接口文档 - 版本 v1.0.0</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <h3><i class="fas fa-chart-bar"></i> 统计信息</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <span class="stat-number">${Object.keys(routes).length}</span>
                        <span class="stat-label">总接口</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${Object.keys(routes).filter(path => path.startsWith('/auth')).length}</span>
                        <span class="stat-label">认证接口</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${Object.keys(routes).filter(path => path.startsWith('/api')).length}</span>
                        <span class="stat-label">业务接口</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">${Object.keys(apiSpec).length}</span>
                        <span class="stat-label">详细文档</span>
                    </div>
                </div>

                <h3><i class="fas fa-search"></i> 快速搜索</h3>
                <input type="text" class="search-box" placeholder="搜索API接口..." id="searchBox">

                <h3><i class="fas fa-list"></i> 接口分类</h3>
                ${generateSidebarNavigation(routes)}

                <div class="nav-section">
                    <h4>服务信息</h4>
                    <div style="font-size: 0.8em; color: #666; line-height: 1.4;">
                        <div><strong>服务地址:</strong> http://127.0.0.1:3007</div>
                        <div><strong>更新时间:</strong> ${new Date().toLocaleString('zh-CN')}</div>
                    </div>
                </div>
            </div>

            <div class="content-area">
                ${generateCollapsibleSections(routes, apiSpec)}
            </div>
        </div>
    </div>

    <script>
        ${generateJavaScript()}
    </script>
</body>
</html>`;
}

// 生成侧边栏导航
function generateSidebarNavigation(routes) {
  const sections = {
    '认证相关': { icon: 'fas fa-lock', apis: [] },
    '用户管理': { icon: 'fas fa-users', apis: [] },
    '部门管理': { icon: 'fas fa-building', apis: [] },
    '职位管理': { icon: 'fas fa-user-tie', apis: [] },
    '岗位管理': { icon: 'fas fa-briefcase', apis: [] },
    '流程管理': { icon: 'fas fa-project-diagram', apis: [] },
    '权限管理': { icon: 'fas fa-shield-alt', apis: [] },
    '待办事项管理': { icon: 'fas fa-tasks', apis: [] },
    '商品管理': { icon: 'fas fa-box', apis: [] },
    '部件管理': { icon: 'fas fa-cogs', apis: [] },
    '跟进管理': { icon: 'fas fa-clipboard-list', apis: [] },
    '图片管理': { icon: 'fas fa-images', apis: [] },
    '序列号管理': { icon: 'fas fa-qrcode', apis: [] }
  };

  Object.entries(routes).forEach(([path, desc]) => {
    if (path.startsWith('/auth')) {
      sections['认证相关'].apis.push({ path, desc });
    } else if (path.includes('/user/')) {
      sections['用户管理'].apis.push({ path, desc });
    } else if (path.includes('/department/')) {
      sections['部门管理'].apis.push({ path, desc });
    } else if (path.includes('/position/')) {
      sections['职位管理'].apis.push({ path, desc });
    } else if (path.includes('/job/')) {
      sections['岗位管理'].apis.push({ path, desc });
    } else if (path.includes('/process/')) {
      sections['流程管理'].apis.push({ path, desc });
    } else if (path.includes('/permission/') || path.includes('/module/')) {
      sections['权限管理'].apis.push({ path, desc });
    } else if (path.includes('/todo/')) {
      sections['待办事项管理'].apis.push({ path, desc });
    } else if (path.includes('/product/')) {
      sections['商品管理'].apis.push({ path, desc });
    } else if (path.includes('/component/')) {
      sections['部件管理'].apis.push({ path, desc });
    } else if (path.includes('/follow/')) {
      sections['跟进管理'].apis.push({ path, desc });
    } else if (path.includes('/image/') || path.includes('/picture/')) {
      sections['图片管理'].apis.push({ path, desc });
    } else if (path.includes('/serialNumber/')) {
      sections['序列号管理'].apis.push({ path, desc });
    }
  });

  return Object.entries(sections)
    .filter(([_, section]) => section.apis.length > 0)
    .map(([sectionName, section]) => `
      <a href="#${sectionName.replace(/\s+/g, '-')}" class="nav-item" onclick="scrollToSection('${sectionName.replace(/\s+/g, '-')}')">
        <i class="${section.icon}"></i> ${sectionName} (${section.apis.length})
      </a>
    `).join('');
}

// 生成可折叠的API章节
function generateCollapsibleSections(routes, apiSpec) {
  const sections = {
    '认证相关': { icon: 'fas fa-lock', apis: [] },
    '用户管理': { icon: 'fas fa-users', apis: [] },
    '部门管理': { icon: 'fas fa-building', apis: [] },
    '职位管理': { icon: 'fas fa-user-tie', apis: [] },
    '岗位管理': { icon: 'fas fa-briefcase', apis: [] },
    '流程管理': { icon: 'fas fa-project-diagram', apis: [] },
    '权限管理': { icon: 'fas fa-shield-alt', apis: [] },
    '待办事项管理': { icon: 'fas fa-tasks', apis: [] },
    '商品管理': { icon: 'fas fa-box', apis: [] },
    '部件管理': { icon: 'fas fa-cogs', apis: [] },
    '跟进管理': { icon: 'fas fa-clipboard-list', apis: [] },
    '图片管理': { icon: 'fas fa-images', apis: [] },
    '序列号管理': { icon: 'fas fa-qrcode', apis: [] }
  };

  Object.entries(routes).forEach(([path, desc]) => {
    if (path.startsWith('/auth')) {
      sections['认证相关'].apis.push({ path, desc });
    } else if (path.includes('/user/')) {
      sections['用户管理'].apis.push({ path, desc });
    } else if (path.includes('/department/')) {
      sections['部门管理'].apis.push({ path, desc });
    } else if (path.includes('/position/')) {
      sections['职位管理'].apis.push({ path, desc });
    } else if (path.includes('/job/')) {
      sections['岗位管理'].apis.push({ path, desc });
    } else if (path.includes('/process/')) {
      sections['流程管理'].apis.push({ path, desc });
    } else if (path.includes('/permission/') || path.includes('/module/')) {
      sections['权限管理'].apis.push({ path, desc });
    } else if (path.includes('/todo/')) {
      sections['待办事项管理'].apis.push({ path, desc });
    } else if (path.includes('/product/')) {
      sections['商品管理'].apis.push({ path, desc });
    } else if (path.includes('/component/')) {
      sections['部件管理'].apis.push({ path, desc });
    } else if (path.includes('/follow/')) {
      sections['跟进管理'].apis.push({ path, desc });
    } else if (path.includes('/image/') || path.includes('/picture/')) {
      sections['图片管理'].apis.push({ path, desc });
    } else if (path.includes('/serialNumber/')) {
      sections['序列号管理'].apis.push({ path, desc });
    }
  });

  return Object.entries(sections)
    .filter(([_, section]) => section.apis.length > 0)
    .map(([sectionName, section]) => `
      <div class="section-group" id="${sectionName.replace(/\s+/g, '-')}">
        <div class="section-header" onclick="toggleSection('${sectionName.replace(/\s+/g, '-')}')">
          <div class="section-title">
            <i class="${section.icon} section-icon"></i>
            ${sectionName}
            <span class="section-count">${section.apis.length}</span>
          </div>
          <i class="fas fa-chevron-down toggle-icon"></i>
        </div>
        <div class="section-content" id="content-${sectionName.replace(/\s+/g, '-')}">
          ${section.apis.map(api => generateCollapsibleApiItem(api.path, api.desc, apiSpec[api.path])).join('')}
        </div>
      </div>
    `).join('');
}

// 生成可折叠的API项目
function generateCollapsibleApiItem(path, description, spec) {
  const method = spec ? spec.method : 'GET';
  const methodClass = `method-${method.toLowerCase()}`;
  const authRequired = spec ? spec.auth : true;
  const authClass = authRequired ? 'auth-required' : 'auth-optional';
  const authText = authRequired ? '需要认证' : '无需认证';

  const apiId = path.replace(/[^a-zA-Z0-9]/g, '-');

  return `
    <div class="api-item">
      <div class="api-header" onclick="toggleApiDetails('${apiId}')">
        <div class="api-basic-info">
          <span class="method-badge ${methodClass}">${method}</span>
          <span class="api-path">${path}</span>
          <span class="api-description">${description}</span>
        </div>
        <div>
          <span class="auth-badge ${authClass}">${authText}</span>
          <i class="fas fa-chevron-down toggle-icon"></i>
        </div>
      </div>
      <div class="api-details" id="details-${apiId}">
        ${generateApiDetails(path, description, spec)}
      </div>
    </div>
  `;
}

// 生成API详细信息
function generateApiDetails(path, description, spec) {
  if (!spec) {
    return `
      <div class="detail-section">
        <div class="detail-title"><i class="fas fa-info-circle"></i> 接口描述</div>
        <p>${description}</p>
        <p><em>此接口暂无详细文档，请联系开发团队获取更多信息。</em></p>
      </div>
    `;
  }

  return `
    <div class="detail-section">
      <div class="detail-title"><i class="fas fa-info-circle"></i> 接口描述</div>
      <p>${spec.description}</p>
    </div>

    ${spec.auth ? `
      <div class="detail-section">
        <div class="detail-title"><i class="fas fa-lock"></i> 认证要求</div>
        <p>此接口需要在请求头中包含有效的JWT令牌：</p>
        <div class="code-block" data-lang="http">Authorization: Bearer &lt;your-jwt-token&gt;</div>
      </div>
    ` : ''}

    ${spec.contentType ? `
      <div class="detail-section">
        <div class="detail-title"><i class="fas fa-file-code"></i> 请求类型</div>
        <p><code>${spec.contentType}</code></p>
      </div>
    ` : ''}

    ${generateParamsSection('🔗 路径参数', spec.pathParams)}
    ${generateParamsSection('❓ 查询参数', spec.queryParams)}
    ${generateParamsSection('📦 请求体参数', spec.requestBody)}
    ${generateResponseSection(spec.response)}
    ${generateExampleSection(spec.example)}
  `;
}

// 生成参数表格
function generateParamsSection(title, params) {
  if (!params || Object.keys(params).length === 0) {
    return '';
  }

  const rows = Object.entries(params).map(([name, param]) => {
    const requiredClass = param.required ? 'required' : 'optional';
    const requiredText = param.required ? '是' : '否';
    const defaultValue = param.default !== undefined ? param.default : '-';
    const enumValues = param.enum ? param.enum.join(', ') : '-';

    return `
      <tr>
        <td class="${requiredClass}">${name}</td>
        <td>${param.type}</td>
        <td class="${requiredClass}">${requiredText}</td>
        <td>${defaultValue}</td>
        <td>${enumValues}</td>
        <td>${param.description || '-'}</td>
      </tr>
    `;
  }).join('');

  return `
    <div class="section">
      <div class="section-title">${title}</div>
      <table class="param-table">
        <thead>
          <tr>
            <th>参数名</th>
            <th>类型</th>
            <th>必填</th>
            <th>默认值</th>
            <th>可选值</th>
            <th>说明</th>
          </tr>
        </thead>
        <tbody>
          ${rows}
        </tbody>
      </table>
    </div>
  `;
}

// 生成响应格式部分
function generateResponseSection(response) {
  if (!response) {
    return '';
  }

  let responseHtml = `
    <div class="section">
      <div class="section-title">📨 响应格式</div>
  `;

  if (response.success) {
    responseHtml += `
      <h4>✅ 成功响应 (${response.success.code || 200})</h4>
      <div class="code-block">${JSON.stringify(response.success, null, 2)}</div>
    `;
  }

  if (response.error) {
    responseHtml += `
      <h4>❌ 错误响应 (${response.error.code || 400})</h4>
      <div class="code-block">${JSON.stringify(response.error, null, 2)}</div>
    `;
  }

  responseHtml += `</div>`;
  return responseHtml;
}

// 生成示例代码部分
function generateExampleSection(example) {
  if (!example) {
    return '';
  }

  return `
    <div class="section">
      <div class="section-title">💡 请求示例</div>
      <h4>cURL 请求</h4>
      <div class="code-block">${example.request}</div>

      <h4>响应示例</h4>
      <div class="code-block">${example.response}</div>
    </div>
  `;
}

// 生成JavaScript代码
function generateJavaScript() {
  return `
    // 切换章节展开/折叠
    function toggleSection(sectionId) {
      const header = document.querySelector('#' + sectionId + ' .section-header');
      const content = document.getElementById('content-' + sectionId);

      header.classList.toggle('active');
      content.classList.toggle('active');
    }

    // 切换API详情展开/折叠
    function toggleApiDetails(apiId) {
      const header = document.querySelector('[onclick*="' + apiId + '"]');
      const details = document.getElementById('details-' + apiId);

      header.classList.toggle('active');
      details.classList.toggle('active');
    }

    // 滚动到指定章节
    function scrollToSection(sectionId) {
      const element = document.getElementById(sectionId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
        // 自动展开该章节
        const content = document.getElementById('content-' + sectionId);
        const header = document.querySelector('#' + sectionId + ' .section-header');
        if (content && !content.classList.contains('active')) {
          header.classList.add('active');
          content.classList.add('active');
        }
      }
    }

    // 搜索功能
    document.getElementById('searchBox').addEventListener('input', function(e) {
      const searchTerm = e.target.value.toLowerCase();
      const apiItems = document.querySelectorAll('.api-item');
      const sections = document.querySelectorAll('.section-group');

      if (searchTerm === '') {
        // 显示所有内容
        apiItems.forEach(item => item.style.display = 'block');
        sections.forEach(section => section.style.display = 'block');
        return;
      }

      sections.forEach(section => {
        const sectionApis = section.querySelectorAll('.api-item');
        let hasVisibleApi = false;

        sectionApis.forEach(item => {
          const path = item.querySelector('.api-path').textContent.toLowerCase();
          const desc = item.querySelector('.api-description').textContent.toLowerCase();

          if (path.includes(searchTerm) || desc.includes(searchTerm)) {
            item.style.display = 'block';
            hasVisibleApi = true;
          } else {
            item.style.display = 'none';
          }
        });

        // 如果章节中有匹配的API，显示章节并展开
        if (hasVisibleApi) {
          section.style.display = 'block';
          const header = section.querySelector('.section-header');
          const content = section.querySelector('.section-content');
          header.classList.add('active');
          content.classList.add('active');
        } else {
          section.style.display = 'none';
        }
      });
    });

    // 页面加载完成后的初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 默认展开第一个章节
      const firstSection = document.querySelector('.section-group');
      if (firstSection) {
        const sectionId = firstSection.id;
        const header = firstSection.querySelector('.section-header');
        const content = firstSection.querySelector('.section-content');
        header.classList.add('active');
        content.classList.add('active');
      }

      // 添加键盘快捷键支持
      document.addEventListener('keydown', function(e) {
        // Ctrl/Cmd + K 聚焦搜索框
        if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
          e.preventDefault();
          document.getElementById('searchBox').focus();
        }

        // ESC 清空搜索
        if (e.key === 'Escape') {
          const searchBox = document.getElementById('searchBox');
          if (searchBox === document.activeElement) {
            searchBox.value = '';
            searchBox.dispatchEvent(new Event('input'));
            searchBox.blur();
          }
        }
      });
    });
  `;
}

module.exports = {
  apiRoutes,
  generateDetailedApiDocs,
  generateSidebarNavigation,
  generateCollapsibleSections,
  generateCollapsibleApiItem,
  generateApiDetails,
  generateParamsSection,
  generateResponseSection,
  generateExampleSection,
  generateJavaScript
};
