// 获取待办事项列表
const getTodoList = (req, res) => {
  try {
    const { keyword = '', status = '', page = 1, size = 10 } = req.query;
    
    // 模拟数据
    const mockData = {
      total: 12,
      items: [
        {
          id: "1",
          orderId: "ORD20230001",
          productName: "产品A",
          productCode: "PA001",
          quantity: 100,
          departmentName: "生产部",
          responsiblePerson: "张三",
          startTime: "2023-06-01 09:00:00",
          endTime: "2023-06-05 18:00:00",
          processNode: "下料",
          status: "待处理",
          remark: "备注信息"
        },
        {
          id: "2",
          orderId: "ORD20230002",
          productName: "产品B",
          productCode: "PB001",
          quantity: 50,
          departmentName: "生产部",
          responsiblePerson: "李四",
          startTime: "2023-06-02 09:00:00",
          endTime: "2023-06-06 18:00:00",
          processNode: "加工",
          status: "进行中",
          remark: "进度正常"
        }
      ]
    };

    // 如果有状态过滤
    if (status) {
      mockData.items = mockData.items.filter(item => item.status === status);
      mockData.total = mockData.items.length;
    }

    // 如果有关键词，进行简单过滤
    if (keyword) {
      mockData.items = mockData.items.filter(item => 
        item.orderId.includes(keyword) || 
        item.productName.includes(keyword) ||
        item.productCode.includes(keyword)
      );
      mockData.total = mockData.items.length;
    }

    res.output(null, 200, mockData, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取订单流程节点跟进详情
const getTodoDetail = (req, res) => {
  try {
    const { id } = req.query;
    
    // 模拟数据
    const mockData = {
      id: id,
      orderId: "ORD20230001",
      orderQrCode: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
      productImage: "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD//gA7Q1JFQVRP...",
      productName: "产品A",
      productCode: "PA001",
      quantity: 100,
      departmentName: "生产部",
      responsiblePerson: "张三",
      startTime: "2023-06-01 09:00:00",
      endTime: "2023-06-05 18:00:00",
      processNode: "下料",
      status: "待处理",
      remark: "备注信息",
      followRecords: [
        {
          id: "1",
          content: "已完成30%",
          images: [
            "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...",
            "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD..."
          ],
          createTime: "2023-06-02 10:00:00",
          createUser: "张三"
        }
      ]
    };

    res.output(null, 200, mockData, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 添加跟进记录
const addTodoRecord = (req, res) => {
  try {
    const { todoId, content, images } = req.body;
    
    // 模拟新增操作
    const newId = Date.now().toString();
    
    res.output(null, 200, { id: newId }, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 更新跟进状态
const updateTodoStatus = (req, res) => {
  try {
    const { id, status, remark } = req.body;
    
    // 模拟更新操作
    res.output(null, 200, null, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

module.exports = {
  getTodoList,
  getTodoDetail,
  addTodoRecord,
  updateTodoStatus
};
