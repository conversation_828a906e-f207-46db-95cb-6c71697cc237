{"_from": "domhandler@^5.0.3", "_id": "domhand<PERSON>@5.0.3", "_inBundle": false, "_integrity": "sha512-cgwlv/1iFQiFnU96XXgROh8xTeetsnJiDsTc7TYCLFd9+/WNkIqPTxiM/8pSd8VIrhXGTf1Ny1q1hquVqDJB5w==", "_location": "/domhandler", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "domhandler@^5.0.3", "name": "<PERSON><PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON><PERSON>", "rawSpec": "^5.0.3", "saveSpec": null, "fetchSpec": "^5.0.3"}, "_requiredBy": ["/cheerio", "/cheerio-select", "/css-select", "/dom-serializer", "/domutils", "/htmlparser2", "/parse5-htmlparser2-tree-adapter"], "_resolved": "https://registry.npmmirror.com/domhandler/-/domhandler-5.0.3.tgz", "_shasum": "cc385f7f751f1d1fc650c21374804254538c7d31", "_spec": "domhandler@^5.0.3", "_where": "D:\\Code\\node-express\\node_modules\\cheerio", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/fb55/domhandler/issues"}, "bundleDependencies": false, "dependencies": {"domelementtype": "^2.3.0"}, "deprecated": false, "description": "Handler for htmlparser2 that turns pages into a dom", "devDependencies": {"@types/jest": "^27.4.1", "@types/node": "^17.0.30", "@typescript-eslint/eslint-plugin": "^5.21.0", "@typescript-eslint/parser": "^5.21.0", "eslint": "^8.14.0", "eslint-config-prettier": "^8.5.0", "htmlparser2": "^8.0.0", "jest": "^27.5.1", "prettier": "^2.6.2", "ts-jest": "^27.1.4", "typescript": "^4.6.4"}, "engines": {"node": ">= 4"}, "exports": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "files": ["lib"], "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}, "homepage": "https://github.com/fb55/domhandler#readme", "jest": {"preset": "ts-jest", "testEnvironment": "node", "moduleNameMapper": {"^(.*)\\.js$": "$1"}}, "keywords": ["dom", "htmlparser2"], "license": "BSD-2-<PERSON><PERSON>", "main": "lib/index.js", "module": "lib/esm/index.js", "name": "<PERSON><PERSON><PERSON><PERSON>", "prettier": {"tabWidth": 4}, "repository": {"type": "git", "url": "git://github.com/fb55/domhandler.git"}, "scripts": {"build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc", "build:esm": "tsc --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run prettier -- --write", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint --ignore-path .gitignore .", "lint:prettier": "npm run prettier -- --check", "prepare": "npm run build", "prettier": "prettier \"**/*.{ts,md,json,yml}\" --ignore-path .gitignore", "test": "npm run test:jest && npm run lint", "test:jest": "jest"}, "sideEffects": false, "types": "lib/index.d.ts", "version": "5.0.3"}