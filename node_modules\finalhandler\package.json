{"_from": "finalhandler@1.1.2", "_id": "finalhandler@1.1.2", "_inBundle": false, "_integrity": "sha512-aAWcW57uxVNrQZqFXjITpW3sIUQmHGG3qSb9mUah9MgMC4NeWhNOlNjXEYq3HjRAvL6arUviZGGJsBg6z0zsWA==", "_location": "/finalhandler", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "finalhandler@1.1.2", "name": "finalhandler", "escapedName": "finalhandler", "rawSpec": "1.1.2", "saveSpec": null, "fetchSpec": "1.1.2"}, "_requiredBy": ["/connect"], "_resolved": "https://registry.npmjs.org/finalhandler/-/finalhandler-1.1.2.tgz", "_shasum": "b7e7d000ffd11938d0fdb053506f6ebabe9f587d", "_spec": "finalhandler@1.1.2", "_where": "D:\\Code\\node-express\\node_modules\\connect", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/pillarjs/finalhandler/issues"}, "bundleDependencies": false, "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "deprecated": false, "description": "Node.js final http responder", "devDependencies": {"eslint": "5.16.0", "eslint-config-standard": "12.0.0", "eslint-plugin-import": "2.17.2", "eslint-plugin-markdown": "1.0.0", "eslint-plugin-node": "8.0.1", "eslint-plugin-promise": "4.1.1", "eslint-plugin-standard": "4.0.0", "istanbul": "0.4.5", "mocha": "6.1.4", "readable-stream": "2.3.6", "safe-buffer": "5.1.2", "supertest": "4.0.2"}, "engines": {"node": ">= 0.8"}, "files": ["LICENSE", "HISTORY.md", "index.js"], "homepage": "https://github.com/pillarjs/finalhandler#readme", "license": "MIT", "name": "finalhandler", "repository": {"type": "git", "url": "git+https://github.com/pillarjs/finalhandler.git"}, "scripts": {"lint": "eslint --plugin markdown --ext js,md .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-ci": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter spec --check-leaks test/", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot --check-leaks test/"}, "version": "1.1.2"}