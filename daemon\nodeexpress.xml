<service>
	<id>nodeexpress.exe</id>
	<name>NodeExpress</name>
	<description>小程序服务端程序</description>
	<executable>D:\nodejs\node.exe</executable>
	<argument>--harmony</argument>
	<argument>E:\DataCode\NodeExpress\node_modules\node-windows\lib\wrapper.js</argument>
	<argument>--file</argument>
	<argument>E:\DataCode\NodeExpress\app.js</argument>
	<argument>--scriptoptions=</argument>
	<argument>--log</argument>
	<argument>NodeExpress wrapper</argument>
	<argument>--grow</argument>
	<argument>0.25</argument>
	<argument>--wait</argument>
	<argument>1</argument>
	<argument>--maxrestarts</argument>
	<argument>3</argument>
	<argument>--abortonerror</argument>
	<argument>n</argument>
	<argument>--stopparentfirst</argument>
	<argument>undefined</argument>
	<logmode>rotate</logmode>
	<stoptimeout>30sec</stoptimeout>
	<serviceaccount>
		<domain>AACD-FENGDONG</domain>
		<user>LocalSystem</user>
		<password></password>
	</serviceaccount>
	<workingdirectory>E:\DataCode\NodeExpress</workingdirectory>
</service>