const express = require('express');
const router = express.Router();
const follow_handler = require('../router_handler/follow');
const expressJoi = require('@escook/express-joi');
const { 
  follow_list_schema, 
  follow_detail_schema, 
  follow_add_schema, 
  follow_update_schema, 
  follow_delete_schema,
  follow_business_schema,
  follow_image_base64_schema
} = require('../schema/follow');

// 获取跟进记录列表
router.get('/list', expressJoi(follow_list_schema), follow_handler.getFollowList);

// 获取跟进记录详情
router.get('/detail', expressJoi(follow_detail_schema), follow_handler.getFollowDetail);

// 添加跟进记录
router.post('/add', follow_handler.addFollow);

// 更新跟进记录
router.post('/update', follow_handler.updateFollow);

// 删除跟进记录
router.post('/delete', expressJoi(follow_delete_schema), follow_handler.deleteFollow);

// 获取图片
router.get('/image/:imageName', follow_handler.getImage);

// 批量上传图片
router.post('/uploadImages', follow_handler.uploadImages);

// 获取业务对象的所有跟进记录
router.get('/business', expressJoi(follow_business_schema), follow_handler.getBusinessFollows);

// 获取图片的Base64编码
router.get('/imageBase64/:imageId', expressJoi(follow_image_base64_schema), follow_handler.getImageBase64);

module.exports = router;
