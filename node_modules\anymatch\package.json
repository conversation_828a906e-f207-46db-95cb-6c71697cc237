{"_from": "anymatch@~3.1.2", "_id": "anymatch@3.1.3", "_inBundle": false, "_integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "_location": "/anymatch", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "anymatch@~3.1.2", "name": "anymatch", "escapedName": "anymatch", "rawSpec": "~3.1.2", "saveSpec": null, "fetchSpec": "~3.1.2"}, "_requiredBy": ["/chokidar"], "_resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "_shasum": "790c58b19ba1720a84205b57c618d5ad8524973e", "_spec": "anymatch@~3.1.2", "_where": "D:\\Code\\node-express\\node_modules\\chokidar", "author": {"name": "<PERSON><PERSON>", "url": "https://github.com/es128"}, "bugs": {"url": "https://github.com/micromatch/anymatch/issues"}, "bundleDependencies": false, "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "deprecated": false, "description": "Matches strings against configurable strings, globs, regular expressions, and/or functions", "devDependencies": {"mocha": "^6.1.3", "nyc": "^14.0.0"}, "engines": {"node": ">= 8"}, "files": ["index.js", "index.d.ts"], "homepage": "https://github.com/micromatch/anymatch", "keywords": ["match", "any", "string", "file", "fs", "list", "glob", "regex", "regexp", "regular", "expression", "function"], "license": "ISC", "name": "anymatch", "repository": {"type": "git", "url": "git+https://github.com/micromatch/anymatch.git"}, "scripts": {"mocha": "mocha", "test": "nyc mocha"}, "version": "3.1.3"}