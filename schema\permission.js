const joi = require('joi');

// 获取权限模板列表的验证规则
const permission_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取权限模板详情的验证规则
const permission_detail_schema = {
  query: {
    id: joi.number().integer().required()
  }
};

// 新增权限模板的验证规则
const permission_add_schema = {
  body: {
    name: joi.string().required(),
    code: joi.string().required(),
    status: joi.string().valid('1', '0').default('1'),
    modules: joi.array().required(),
    description: joi.string().allow('').optional()
  }
};

// 更新权限模板的验证规则
const permission_update_schema = {
  body: {
    id: joi.number().integer().required(),
    name: joi.string().required(),
    code: joi.string().required(),
    status: joi.string().valid('1', '0').default('1'),
    modules: joi.array().required(),
    description: joi.string().allow('').optional()
  }
};

// 删除权限模板的验证规则
const permission_delete_schema = {
  body: {
    id: joi.number().integer().required()
  }
};

module.exports = {
  permission_list_schema,
  permission_detail_schema,
  permission_add_schema,
  permission_update_schema,
  permission_delete_schema
};
