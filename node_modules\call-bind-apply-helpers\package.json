{"_from": "call-bind-apply-helpers@^1.0.2", "_id": "call-bind-apply-helpers@1.0.2", "_inBundle": false, "_integrity": "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ==", "_location": "/call-bind-apply-helpers", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "call-bind-apply-helpers@^1.0.2", "name": "call-bind-apply-helpers", "escapedName": "call-bind-apply-helpers", "rawSpec": "^1.0.2", "saveSpec": null, "fetchSpec": "^1.0.2"}, "_requiredBy": ["/call-bound", "/dunder-proto", "/get-intrinsic"], "_resolved": "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz", "_shasum": "4b5428c222be985d79c3d82657479dbe0b59b2d6", "_spec": "call-bind-apply-helpers@^1.0.2", "_where": "D:\\Code\\node-express\\node_modules\\get-intrinsic", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "bugs": {"url": "https://github.com/ljharb/call-bind-apply-helpers/issues"}, "bundleDependencies": false, "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "deprecated": false, "description": "Helper functions around Function call/apply/bind, for use in `call-bind`", "devDependencies": {"@arethetypeswrong/cli": "^0.17.3", "@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.3", "@types/for-each": "^0.3.3", "@types/function-bind": "^1.1.10", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.8.1", "auto-changelog": "^2.5.0", "encoding": "^0.1.13", "es-value-fixtures": "^1.7.1", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.5", "has-strict-mode": "^1.1.0", "in-publish": "^2.0.1", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.4", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "engines": {"node": ">= 0.4"}, "exports": {".": "./index.js", "./actualApply": "./actualApply.js", "./applyBind": "./applyBind.js", "./functionApply": "./functionApply.js", "./functionCall": "./functionCall.js", "./reflectApply": "./reflectApply.js", "./package.json": "./package.json"}, "homepage": "https://github.com/ljharb/call-bind-apply-helpers#readme", "license": "MIT", "main": "index.js", "name": "call-bind-apply-helpers", "publishConfig": {"ignore": [".github/workflows"]}, "repository": {"type": "git", "url": "git+https://github.com/ljharb/call-bind-apply-helpers.git"}, "scripts": {"lint": "eslint --ext=.js,.mjs .", "postlint": "tsc -p . && attw -P", "posttest": "npx npm@'>=10.2' audit --production", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\"", "prelint": "evalmd README.md", "prepack": "npmignore --auto --commentLines=auto", "prepublish": "not-in-publish || npm run prepublishOnly", "prepublishOnly": "safe-publish-latest", "pretest": "npm run lint", "test": "npm run tests-only", "tests-only": "nyc tape 'test/**/*.js'", "version": "auto-changelog && git add CHANGELOG.md"}, "testling": {"files": "test/index.js"}, "version": "1.0.2"}