{"_from": "after@0.8.2", "_id": "after@0.8.2", "_inBundle": false, "_integrity": "sha512-QbJ0NTQ/I9DI3uSJA4cbexiwQeRAfjPScqIbSjUDd9TOrcg6pTkdgziesOqxBMBzit8vFCTwrP27t13vFOORRA==", "_location": "/after", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "after@0.8.2", "name": "after", "escapedName": "after", "rawSpec": "0.8.2", "saveSpec": null, "fetchSpec": "0.8.2"}, "_requiredBy": ["/engine.io-parser"], "_resolved": "https://registry.npmjs.org/after/-/after-0.8.2.tgz", "_shasum": "fedb394f9f0e02aa9768e702bda23b505fae7e1f", "_spec": "after@0.8.2", "_where": "D:\\Code\\node-express\\node_modules\\engine.io-parser", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/Raynos/after/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://raynos.org"}], "deprecated": false, "description": "after - tiny flow control", "devDependencies": {"mocha": "~1.8.1"}, "homepage": "https://github.com/Raynos/after#readme", "keywords": ["flowcontrol", "after", "flow", "control", "arch"], "license": "MIT", "name": "after", "repository": {"type": "git", "url": "git://github.com/Raynos/after.git"}, "scripts": {"test": "mocha --ui tdd --reporter spec test/*.js"}, "version": "0.8.2"}