const joi = require('joi');

// 获取待办事项列表的验证规则
const todo_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    status: joi.string().valid('待处理', '进行中', '已完成', '已取消').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

// 获取待办事项详情的验证规则
const todo_detail_schema = {
  query: {
    id: joi.string().required()
  }
};

// 添加跟进记录的验证规则
const todo_add_record_schema = {
  body: {
    todoId: joi.string().required(),
    content: joi.string().required(),
    images: joi.array().items(joi.string()).optional()
  }
};

// 更新跟进状态的验证规则
const todo_update_status_schema = {
  body: {
    id: joi.string().required(),
    status: joi.string().valid('待处理', '进行中', '已完成', '已取消').required(),
    remark: joi.string().allow('').optional()
  }
};

module.exports = {
  todo_list_schema,
  todo_detail_schema,
  todo_add_record_schema,
  todo_update_status_schema
};
