{"_from": "parse5-htmlparser2-tree-adapter@^7.0.0", "_id": "parse5-htmlparser2-tree-adapter@7.1.0", "_inBundle": false, "_integrity": "sha512-ruw5xyKs6lrpo9x9rCZqZZnIUntICjQAd0Wsmp396Ul9lN/h+ifgVV1x1gZHi8euej6wTfpqX8j+BFQxF0NS/g==", "_location": "/parse5-htmlparser2-tree-adapter", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "parse5-htmlparser2-tree-adapter@^7.0.0", "name": "parse5-htmlparser2-tree-adapter", "escapedName": "parse5-htmlparser2-tree-adapter", "rawSpec": "^7.0.0", "saveSpec": null, "fetchSpec": "^7.0.0"}, "_requiredBy": ["/cheerio"], "_resolved": "https://registry.npmmirror.com/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-7.1.0.tgz", "_shasum": "b5a806548ed893a43e24ccb42fbb78069311e81b", "_spec": "parse5-htmlparser2-tree-adapter@^7.0.0", "_where": "D:\\Code\\node-express\\node_modules\\cheerio", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/inikulin"}, "bugs": {"url": "https://github.com/inikulin/parse5/issues"}, "bundleDependencies": false, "contributors": "https://github.com/inikulin/parse5/graphs/contributors", "dependencies": {"domhandler": "^5.0.3", "parse5": "^7.0.0"}, "deprecated": false, "description": "htmlparser2 tree adapter for parse5.", "exports": {"import": "./dist/index.js", "require": "./dist/cjs/index.js"}, "files": ["dist/cjs/package.json", "dist/**/*.js", "dist/**/*.d.ts"], "funding": "https://github.com/inikulin/parse5?sponsor=1", "homepage": "https://parse5.js.org", "keywords": ["parse5", "parser", "tree adapter", "htmlparser2"], "license": "MIT", "main": "dist/cjs/index.js", "module": "dist/index.js", "name": "parse5-htmlparser2-tree-adapter", "repository": {"type": "git", "url": "git://github.com/inikulin/parse5.git"}, "scripts": {"build:cjs": "tsc --moduleResolution node10 --module CommonJS --target ES6 --outDir dist/cjs && echo '{\"type\":\"commonjs\"}' > dist/cjs/package.json"}, "type": "module", "types": "dist/index.d.ts", "version": "7.1.0"}