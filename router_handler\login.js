const db = require("../db/index");
const log = require('./log')
const jwt = require("jsonwebtoken");
const config = require("../config");
const multer = require('multer');
const path = require('path');
const fs = require('fs');
const {
  IMAGE_TYPES,
  IMAGE_PATHS 
} = require('../utils/imageManager');

// 注册处理函数
exports.userRegister = (req, res) => {
    try {
        // 验证必要的字段
        const { username, password, phone, name, gender, birthday, idCard } = req.body;

        if (!username || !password) {
            return res.output(new Error("用户名和密码不能为空"), 400);
        }

        let role = "user";

        // 检查用户名是否已存在
        const sql = "select username from users where username=?";
        db.query(sql, username, (err, results) => {
            if (err) return res.output(err);
            if (results.length === 1) return res.output(new Error("用户名被占用"), 400);

            // 插入用户信息
            const sqlStr = "insert into users set ?";
            db.query(sqlStr, { username, password, phone, name, gender, birthday, idCard, role }, (err, results) => {
                if (err) return res.output(err);
                if (results.affectedRows !== 1) return res.output(new Error("用户注册失败！"), 400);

                // 记录日志
                log.add("sysLog", "注册用户", username);

                // 确保返回格式与Android应用期望的一致
                res.output(null, 200, {
                    username: username,
                    status: "0",
                    message: "注册成功"
                });
            });
        });
    } catch (error) {
        // 捕获任何未处理的异常
        console.error("注册过程中发生错误:", error);
        return res.output(new Error("注册过程中发生错误: " + error.message), 500);
    }
};

// 登录处理函数
exports.userLogin = (req, res) => {
    try {
        // 验证必要的字段
        const { username, password } = req.body;

        if (!username || !password) {
            return res.output(new Error("用户名和密码不能为空"), 400);
        }

        const sql = "select * from users where username=?";
        db.query(sql, username, (err, results) => {
            if (err) return res.output(err);
            if (results.length !== 1) return res.output(new Error("登录失败"), 400);
            if (password !== results[0].password) {
                return res.output(new Error("密码错误，登录失败！"), 400);
            }

            // 创建用户对象，移除密码
            const user = { ...results[0], password: "" };

            // 生成JWT令牌
            const token = jwt.sign(user, config.jwtSecretKey, {
                expiresIn: config.expiresIn,
            });

            // 获取用户角色
            const role = results[0].role || "user";

            // 记录日志
            log.add("sysLog", "登录系统", username);

            // 返回成功信息
            res.output(null, 200, {
                token: token,
                username: username,
                role: role
            });
        });
    } catch (error) {
        // 捕获任何未处理的异常
        console.error("登录过程中发生错误:", error);
        return res.output(new Error("登录过程中发生错误: " + error.message), 500);
    }
};

// 注册并上传图像处理函数
exports.registerWithAvatar = (req, res) => {
    try {
        // 记录请求信息，方便调试
        console.log("收到注册请求，请求头:", req.headers);
        console.log("请求方法:", req.method);
        console.log("Content-Type:", req.headers['content-type']);

        // 检查Content-Type是否为multipart/form-data
        const contentType = req.headers['content-type'] || '';
        if (contentType.includes('multipart/form-data')) {
            console.log("检测到multipart/form-data请求，将使用multer处理");

            // 创建存储配置
            const storage = multer.diskStorage({
                destination: function (_, __, cb) {
                    // 使用Android客户端期望的路径
                    const uploadPath = 'images/avatar/';
                    // 确保目录存在
                    if (!fs.existsSync(uploadPath)) {
                        fs.mkdirSync(uploadPath, { recursive: true });
                    }
                    cb(null, uploadPath);
                },
                filename: function (_, file, cb) {
                    // 使用时间戳生成唯一文件名
                    const timestamp = Date.now();
                    const extension = path.extname(file.originalname);
                    const filename = `avatar-${timestamp}.${extension}`;
                    cb(null, filename);
                }
            });

            // 配置multer，允许所有字段
            const upload = multer({
                storage: storage,
                fileFilter: function (_, file, cb) {
                    // 接受所有类型的文件，因为Android客户端可能发送mimetype为image/*
                    cb(null, true);
                }
            }).fields([
                { name: 'avatar', maxCount: 1 },
                { name: 'registerRequest', maxCount: 1 },
                { name: 'imgRequest', maxCount: 1 }
            ]);

            // 使用multer处理请求
            upload(req, res, function (err) {
                if (err instanceof multer.MulterError) {
                    console.error("Multer错误:", err.message);
                    return res.output(new Error("文件上传失败: " + err.message), 400, null, false);
                } else if (err) {
                    console.error("上传错误:", err.message);
                    return res.output(new Error("服务器错误: " + err.message), 500, null, false);
                }

                console.log("Multer处理后的请求体:", req.body);
                console.log("Multer处理后的文件:", req.files);

                // 继续处理请求
                processRegistration(req, res);
            });
            return;
        }

        // 如果不是multipart/form-data，检查请求体是否为空
        console.log("收到注册请求，请求体:", req.body);

        if (!req.body) {
            return res.output(new Error("请求体完全为空 - 请确保设置了正确的Content-Type头"), 400, null, false);
        }

        // 检查请求体是否为空对象
        if (Object.keys(req.body).length === 0) {
            console.log("请求体为空对象，但存在req.body");

            // 提供更详细的错误信息
            let errorMsg = `请求体为空对象 - 当前Content-Type: ${contentType}`;

            if (contentType.includes('application/json')) {
                errorMsg += "。请确保请求体包含有效的JSON数据。";
            } else if (contentType.includes('application/x-www-form-urlencoded')) {
                errorMsg += "。请确保表单数据正确提交。";
            } else {
                errorMsg += "。请使用正确的Content-Type: application/json 或 multipart/form-data。";
            }

            return res.output(new Error(errorMsg), 400, null, false);
        }

        // 继续处理请求
        processRegistration(req, res);
    } catch (error) {
        console.error("处理注册请求时发生错误:", error);
        return res.output(new Error("处理注册请求时发生错误: " + error.message), 500, null, false);
    }
};

// 处理注册逻辑
function processRegistration(req, res) {
    try {

        // 获取请求数据
        let username, password, phone, name, gender, birthday, idCard;
        let avatarFile = null;
        let imgData = {};

        // 处理不同类型的请求
        if (req.files && req.files.avatar && req.body.registerRequest) {
            // 处理Android客户端发送的带文件的multipart/form-data请求
            console.log("处理带文件的multipart/form-data请求");

            // 获取上传的文件
            avatarFile = req.files.avatar[0];
            console.log("上传的文件:", avatarFile);

            // 解析registerRequest字段
            try {
                const registerData = typeof req.body.registerRequest === 'string'
                    ? JSON.parse(req.body.registerRequest)
                    : req.body.registerRequest;

                console.log("解析的registerRequest:", registerData);

                username = registerData.username;
                password = registerData.password;
                phone = registerData.phone || '';
                name = registerData.name || '';
                gender = registerData.gender || '';
                birthday = registerData.birthday || '';
                idCard = registerData.idCard || '';
            } catch (error) {
                console.error("解析registerRequest失败:", error.message);
                return res.output(new Error("解析registerRequest失败: " + error.message), 400, null, false);
            }

            // 如果有imgRequest字段，尝试解析它
            if (req.body.imgRequest) {
                try {
                    imgData = typeof req.body.imgRequest === 'string'
                        ? JSON.parse(req.body.imgRequest)
                        : req.body.imgRequest;
                    console.log("解析的imgRequest:", imgData);
                } catch (error) {
                    console.error("解析imgRequest失败:", error.message);
                }
            }
        } else if (req.files && req.files.avatar) {
            // 处理带文件的multipart/form-data请求（直接字段）
            console.log("处理带文件的multipart/form-data请求（直接字段）");

            // 获取上传的文件
            avatarFile = req.files.avatar[0];
            console.log("上传的文件:", avatarFile);

            // 从请求体中获取字段
            username = req.body.username;
            password = req.body.password;
            phone = req.body.phone || '';
            name = req.body.name || '';
            gender = req.body.gender || '';
            birthday = req.body.birthday || '';
            idCard = req.body.idCard || '';

            // 如果有imgRequest字段，尝试解析它
            if (req.body.imgRequest) {
                try {
                    imgData = typeof req.body.imgRequest === 'string'
                        ? JSON.parse(req.body.imgRequest)
                        : req.body.imgRequest;
                } catch (error) {
                    console.error("解析imgRequest失败:", error.message);
                }
            }
        } else if (req.body.username && req.body.password) {
            // 处理直接提交的表单字段
            console.log("处理直接提交的表单字段");

            username = req.body.username;
            password = req.body.password;
            phone = req.body.phone || '';
            name = req.body.name || '';
            gender = req.body.gender || '';
            birthday = req.body.birthday || '';
            idCard = req.body.idCard || '';
        } else if (req.body.registerRequest) {
            // 处理registerRequest字段（无文件）
            console.log("处理registerRequest字段（无文件）");

            let registerData;
            try {
                registerData = typeof req.body.registerRequest === 'string'
                    ? JSON.parse(req.body.registerRequest)
                    : req.body.registerRequest;

                console.log("解析的registerRequest:", registerData);

                username = registerData.username;
                password = registerData.password;
                phone = registerData.phone || '';
                name = registerData.name || '';
                gender = registerData.gender || '';
                birthday = registerData.birthday || '';
                idCard = registerData.idCard || '';
            } catch (error) {
                console.error("解析registerRequest失败:", error.message);
                return res.output(new Error("解析registerRequest失败: " + error.message), 400, null, false);
            }

            // 如果有imgRequest字段，尝试解析它
            if (req.body.imgRequest) {
                try {
                    imgData = typeof req.body.imgRequest === 'string'
                        ? JSON.parse(req.body.imgRequest)
                        : req.body.imgRequest;
                    console.log("解析的imgRequest:", imgData);
                } catch (error) {
                    console.error("解析imgRequest失败:", error.message);
                }
            }
        } else {
            // 尝试从JSON请求体中获取数据
            console.log("尝试从JSON请求体中获取数据");

            if (typeof req.body === 'object' && req.body !== null) {
                username = req.body.username;
                password = req.body.password;
                phone = req.body.phone || '';
                name = req.body.name || '';
                gender = req.body.gender || '';
                birthday = req.body.birthday || '';
                idCard = req.body.idCard || '';
            }
        }

        // 验证必要的字段
        if (!username || !password) {
            return res.output(new Error("用户名和密码不能为空"), 400, null, false);
        }

        console.log("处理的注册数据:", { username, password, phone, name, gender, birthday, idCard });

        // 设置默认值
        let role = "user";
        let avatarUrl = "";
        let avatarPath = IMAGE_PATHS.AVATAR;

        // 如果有上传的文件，处理文件路径
        if (avatarFile) {
            avatarPath = avatarFile.path;
            console.log("上传的文件路径:", avatarPath);
        }

        // 检查用户名是否已存在
        const sql = "select username from users where username=?";
        db.query(sql, username, (err, results) => {
            if (err) {
                // 如果上传了文件但查询出错，删除已上传的文件
                if (avatarPath && fs.existsSync(avatarPath)) {
                    fs.unlinkSync(avatarPath);
                }
                return res.output(err, 500, null, false);
            }

            if (results.length === 1) {
                // 用户名已存在，删除已上传的文件
                if (avatarPath && fs.existsSync(avatarPath)) {
                    fs.unlinkSync(avatarPath);
                }
                return res.output(new Error("用户名被占用"), 400, null, false);
            }

            // 插入用户信息
            if (avatarPath) {
                // 如果有上传文件，获取文件名
                const filename = path.basename(avatarPath);
                avatarUrl = config.avatarPath + filename;
            }

            const sqlStr = "insert into users set ?";
            db.query(sqlStr, { username, password, phone, name, gender, birthday, idCard, role, avatarUrl }, (err, results) => {
                if (err) {
                    // 如果插入用户信息失败，删除已上传的文件
                    if (avatarPath && fs.existsSync(avatarPath)) {
                        fs.unlinkSync(avatarPath);
                    }
                    return res.output(err, 500, null, false);
                }

                if (results.affectedRows !== 1) {
                    // 如果插入用户信息失败，删除已上传的文件
                    if (avatarPath && fs.existsSync(avatarPath)) {
                        fs.unlinkSync(avatarPath);
                    }
                    return res.output(new Error("用户注册失败！"), 400, null, false);
                }

                // 记录日志
                log.add("sysLog", "注册用户并上传头像", username);

                // 返回成功信息
                // 确保返回格式与Android应用期望的一致
                res.output(null, 200, {
                    username: username,
                    status: "0",
                    avatarPath: avatarUrl,
                    message: "注册成功"
                }, true);
            });
        });
    } catch (error) {
        // 捕获任何未处理的异常
        console.error("处理注册逻辑时发生错误:", error);
        return res.output(new Error("处理注册逻辑时发生错误: " + error.message), 500, null, false);
    }
}

exports.checkUsername = (req, res) => {
    const username = req.query.username;
    const sql = 'select * from users where username= ?'
    db.query(sql, username, (err, results) => {
        if (err) return res.output(err)
        if (results.length !== 1) return res.output(new Error("获取用户信息失败！"), 400)
        // 移除密码字段
        const userInfo = { ...results[0] };
        delete userInfo.password;
        res.output(null, 200, userInfo);
    })
}
