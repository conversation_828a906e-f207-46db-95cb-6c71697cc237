const joi = require('joi');

const customer_list_schema = {
  query: {
    keyword: joi.string().allow('').optional(),
    page: joi.number().integer().min(1).default(1),
    size: joi.number().integer().min(1).max(100).default(10)
  }
};

const customer_detail_schema = {
  query: {
    id: joi.number().integer().required()
  }
};

const customer_add_schema = {
  body: {
    name: joi.string().required(),
    code: joi.string().required(),
    companyName: joi.string().required(),
    address: joi.string().required(),
    contact: joi.string().required(),
    phone: joi.string().required(),
    status: joi.string().valid('1', '0').default('1'),
    remark: joi.string().allow('')
  }
};

const customer_update_schema = {
  body: {
    id: joi.number().integer().required(),
    name: joi.string().required(),
    code: joi.string().required(),
    companyName: joi.string().required(),
    address: joi.string().required(),
    contact: joi.string().required(),
    phone: joi.string().required(),
    status: joi.string().valid('1', '0').default('1'),
    remark: joi.string().allow('')
  }
};

const customer_delete_schema = {
  body: {
    id: joi.number().integer().required()
  }
};

module.exports = {
  customer_list_schema,
  customer_detail_schema,
  customer_add_schema,
  customer_update_schema,
  customer_delete_schema
};