// API接口详细规范配置
const apiSpec = {
  // 认证相关
  '/auth/login': {
    method: 'POST',
    description: '用户登录',
    auth: false,
    requestBody: {
      username: { type: 'string', required: true, description: '用户名' },
      password: { type: 'string', required: true, description: '密码' }
    },
    response: {
      success: {
        code: 200,
        data: {
          token: 'JWT令牌',
          userInfo: {
            id: '用户ID',
            username: '用户名',
            name: '姓名',
            role: '角色'
          }
        }
      },
      error: {
        code: 400,
        message: '用户名或密码错误'
      }
    },
    example: {
      request: `curl -X POST http://127.0.0.1:3007/auth/login \\
  -H "Content-Type: application/json" \\
  -d '{"username":"admin","password":"123456"}'`,
      response: `{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "userInfo": {
      "id": "1",
      "username": "admin",
      "name": "管理员"
    }
  },
  "success": true
}`
    }
  },

  '/auth/register': {
    method: 'POST',
    description: '用户基本注册',
    auth: false,
    requestBody: {
      username: { type: 'string', required: true, description: '用户名' },
      password: { type: 'string', required: true, description: '密码' },
      name: { type: 'string', required: true, description: '姓名' },
      email: { type: 'string', required: false, description: '邮箱' }
    },
    response: {
      success: {
        code: 200,
        message: '注册成功'
      }
    }
  },

  '/auth/registerWithAvatar': {
    method: 'POST',
    description: '用户注册并上传图像',
    auth: false,
    contentType: 'multipart/form-data',
    requestBody: {
      username: { type: 'string', required: true, description: '用户名' },
      password: { type: 'string', required: true, description: '密码' },
      name: { type: 'string', required: true, description: '姓名' },
      avatar: { type: 'file', required: true, description: '头像图片文件' }
    },
    response: {
      success: {
        code: 200,
        data: {
          userId: '用户ID',
          avatarUrl: '头像访问地址'
        }
      }
    }
  },

  // 用户管理
  '/api/user/profile': {
    method: 'GET',
    description: '获取已登录用户信息',
    auth: true,
    response: {
      success: {
        code: 200,
        data: {
          id: '用户ID',
          username: '用户名',
          name: '姓名',
          email: '邮箱',
          phone: '电话',
          avatarUrl: '头像地址'
        }
      }
    }
  },

  '/api/user/updateAvatar': {
    method: 'POST',
    description: '更新用户图像',
    auth: true,
    contentType: 'multipart/form-data',
    requestBody: {
      avatar: { type: 'file', required: true, description: '头像图片文件' },
      userId: { type: 'string', required: false, description: '用户ID（可选）' }
    },
    response: {
      success: {
        code: 200,
        data: {
          message: '头像更新成功',
          avatarUrl: '新头像访问地址',
          imageId: '图片ID'
        }
      }
    }
  },

  '/api/user/all': {
    method: 'GET',
    description: '获取所有用户信息',
    auth: true,
    queryParams: {
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' },
      keyword: { type: 'string', required: false, description: '搜索关键词' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          page: '当前页码',
          size: '每页数量',
          list: [
            {
              id: '用户ID',
              username: '用户名',
              name: '姓名',
              status: '状态'
            }
          ]
        }
      }
    }
  },

  '/api/user/checkuser': {
    method: 'GET',
    description: '审核用户',
    auth: true,
    response: {
      success: {
        code: 200,
        data: {
          pendingUsers: '待审核用户列表'
        }
      }
    }
  },

  '/api/user/updateProfile': {
    method: 'POST',
    description: '修改用户信息',
    auth: true,
    requestBody: {
      name: { type: 'string', required: false, description: '姓名' },
      email: { type: 'string', required: false, description: '邮箱' },
      phone: { type: 'string', required: false, description: '电话' }
    },
    response: {
      success: {
        code: 200,
        message: '用户信息更新成功'
      }
    }
  },

  '/api/user/changePassword': {
    method: 'POST',
    description: '修改密码',
    auth: true,
    requestBody: {
      oldPassword: { type: 'string', required: true, description: '原密码' },
      newPassword: { type: 'string', required: true, description: '新密码' }
    },
    response: {
      success: {
        code: 200,
        message: '密码修改成功'
      }
    }
  },

  '/api/user/updateUser': {
    method: 'POST',
    description: '更新用户信息（管理员）',
    auth: true,
    requestBody: {
      username: { type: 'string', required: true, description: '用户名' },
      name: { type: 'string', required: false, description: '姓名' },
      gender: { type: 'string', required: false, description: '性别' },
      department: { type: 'string', required: false, description: '部门' },
      position: { type: 'string', required: false, description: '职位' },
      job: { type: 'string', required: false, description: '岗位' },
      role: { type: 'string', required: false, description: '角色' }
    },
    response: {
      success: {
        code: 200,
        message: '用户信息更新成功'
      }
    }
  },

  '/api/user/delete': {
    method: 'POST',
    description: '删除用户',
    auth: true,
    requestBody: {
      userId: { type: 'string', required: true, description: '用户ID' }
    },
    response: {
      success: {
        code: 200,
        message: '用户删除成功'
      }
    }
  },

  // 部门管理
  '/api/department/list': {
    method: 'GET',
    description: '获取部门列表',
    auth: true,
    queryParams: {
      keyword: { type: 'string', required: false, description: '搜索关键词' },
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          items: [
            {
              id: '部门ID',
              name: '部门名称',
              code: '部门编码',
              parentId: '上级部门ID',
              status: '状态'
            }
          ]
        }
      }
    }
  },

  '/api/department/add': {
    method: 'POST',
    description: '新增部门',
    auth: true,
    requestBody: {
      name: { type: 'string', required: true, description: '部门名称' },
      code: { type: 'string', required: true, description: '部门编码' },
      parentId: { type: 'string', required: false, default: '0', description: '上级部门ID' },
      status: { type: 'string', required: false, default: '正常', description: '状态' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '新部门ID'
        }
      }
    }
  },

  '/api/department/detail': {
    method: 'GET',
    description: '获取部门详情',
    auth: true,
    queryParams: {
      id: { type: 'string', required: true, description: '部门ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '部门ID',
          name: '部门名称',
          code: '部门编码',
          parentId: '上级部门ID',
          status: '状态',
          createTime: '创建时间'
        }
      }
    }
  },

  '/api/department/update': {
    method: 'POST',
    description: '更新部门',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '部门ID' },
      name: { type: 'string', required: false, description: '部门名称' },
      code: { type: 'string', required: false, description: '部门编码' },
      parentId: { type: 'string', required: false, description: '上级部门ID' },
      status: { type: 'string', required: false, description: '状态' }
    },
    response: {
      success: {
        code: 200,
        message: '部门更新成功'
      }
    }
  },

  '/api/department/delete': {
    method: 'POST',
    description: '删除部门',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '部门ID' }
    },
    response: {
      success: {
        code: 200,
        message: '部门删除成功'
      }
    }
  },

  '/api/department': {
    method: 'GET',
    description: '获取部门下拉框数据(Android专用)',
    auth: false,
    response: {
      success: {
        code: 200,
        data: [
          {
            id: '部门ID',
            name: '部门名称',
            code: '部门编码'
          }
        ]
      }
    },
    example: {
      request: `curl -X GET http://127.0.0.1:3007/api/department`,
      response: `{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "1",
      "name": "研发部",
      "code": "RD001"
    },
    {
      "id": "2",
      "name": "市场部",
      "code": "MK001"
    }
  ],
  "success": true
}`
    }
  },

  '/api/position': {
    method: 'GET',
    description: '获取职位下拉框数据(Android专用)',
    auth: false,
    response: {
      success: {
        code: 200,
        data: [
          {
            id: '职位ID',
            name: '职位名称',
            code: '职位编码'
          }
        ]
      }
    },
    example: {
      request: `curl -X GET http://127.0.0.1:3007/api/position`,
      response: `{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "1",
      "name": "工程师",
      "code": "ENG001"
    },
    {
      "id": "2",
      "name": "经理",
      "code": "MGR001"
    }
  ],
  "success": true
}`
    }
  },

  '/api/job': {
    method: 'GET',
    description: '获取岗位下拉框数据(Android专用)',
    auth: false,
    response: {
      success: {
        code: 200,
        data: [
          {
            id: '岗位ID',
            name: '岗位名称',
            code: '岗位编码'
          }
        ]
      }
    },
    example: {
      request: `curl -X GET http://127.0.0.1:3007/api/job`,
      response: `{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": "1",
      "name": "前端开发",
      "code": "FE001"
    },
    {
      "id": "2",
      "name": "后端开发",
      "code": "BE001"
    }
  ],
  "success": true
}`
    }
  },

  // 职位管理
  '/api/position/list': {
    method: 'GET',
    description: '获取职位列表',
    auth: true,
    queryParams: {
      keyword: { type: 'string', required: false, description: '搜索关键词' },
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          items: [
            {
              id: '职位ID',
              name: '职位名称',
              code: '职位编码',
              status: '状态'
            }
          ]
        }
      }
    }
  },

  '/api/position/detail': {
    method: 'GET',
    description: '获取职位详情',
    auth: true,
    queryParams: {
      id: { type: 'string', required: true, description: '职位ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '职位ID',
          name: '职位名称',
          code: '职位编码',
          status: '状态',
          createTime: '创建时间'
        }
      }
    }
  },

  '/api/position/add': {
    method: 'POST',
    description: '新增职位',
    auth: true,
    requestBody: {
      name: { type: 'string', required: true, description: '职位名称' },
      code: { type: 'string', required: true, description: '职位编码' },
      status: { type: 'string', required: false, default: '正常', description: '状态' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '新职位ID'
        }
      }
    }
  },

  '/api/position/update': {
    method: 'POST',
    description: '更新职位',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '职位ID' },
      name: { type: 'string', required: false, description: '职位名称' },
      code: { type: 'string', required: false, description: '职位编码' },
      status: { type: 'string', required: false, description: '状态' }
    },
    response: {
      success: {
        code: 200,
        message: '职位更新成功'
      }
    }
  },

  '/api/position/delete': {
    method: 'POST',
    description: '删除职位',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '职位ID' }
    },
    response: {
      success: {
        code: 200,
        message: '职位删除成功'
      }
    }
  },

  // 岗位管理
  '/api/post/list': {
    method: 'GET',
    description: '获取岗位列表',
    auth: true,
    queryParams: {
      keyword: { type: 'string', required: false, description: '搜索关键词' },
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          items: [
            {
              id: '岗位ID',
              name: '岗位名称',
              code: '岗位编码',
              status: '状态'
            }
          ]
        }
      }
    }
  },

  '/api/post/detail': {
    method: 'GET',
    description: '获取岗位详情',
    auth: true,
    queryParams: {
      id: { type: 'string', required: true, description: '岗位ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '岗位ID',
          name: '岗位名称',
          code: '岗位编码',
          status: '状态',
          createTime: '创建时间'
        }
      }
    }
  },

  '/api/post/add': {
    method: 'POST',
    description: '新增岗位',
    auth: true,
    requestBody: {
      name: { type: 'string', required: true, description: '岗位名称' },
      code: { type: 'string', required: true, description: '岗位编码' },
      status: { type: 'string', required: false, default: '正常', description: '状态' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '新岗位ID'
        }
      }
    }
  },

  '/api/post/update': {
    method: 'POST',
    description: '更新岗位',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '岗位ID' },
      name: { type: 'string', required: false, description: '岗位名称' },
      code: { type: 'string', required: false, description: '岗位编码' },
      status: { type: 'string', required: false, description: '状态' }
    },
    response: {
      success: {
        code: 200,
        message: '岗位更新成功'
      }
    }
  },

  '/api/post/delete': {
    method: 'POST',
    description: '删除岗位',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '岗位ID' }
    },
    response: {
      success: {
        code: 200,
        message: '岗位删除成功'
      }
    }
  },

  // 流程管理
  '/api/process/list': {
    method: 'GET',
    description: '获取流程节点列表',
    auth: true,
    queryParams: {
      keyword: { type: 'string', required: false, description: '搜索关键词' },
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          items: [
            {
              id: '流程节点ID',
              name: '节点名称',
              code: '节点编码',
              order: '排序',
              status: '状态'
            }
          ]
        }
      }
    }
  },

  '/api/process/detail': {
    method: 'GET',
    description: '获取流程节点详情',
    auth: true,
    queryParams: {
      id: { type: 'string', required: true, description: '流程节点ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '流程节点ID',
          name: '节点名称',
          code: '节点编码',
          order: '排序',
          status: '状态',
          createTime: '创建时间'
        }
      }
    }
  },

  '/api/process/add': {
    method: 'POST',
    description: '新增流程节点',
    auth: true,
    requestBody: {
      name: { type: 'string', required: true, description: '节点名称' },
      code: { type: 'string', required: true, description: '节点编码' },
      order: { type: 'number', required: false, description: '排序' },
      status: { type: 'string', required: false, default: '正常', description: '状态' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '新流程节点ID'
        }
      }
    }
  },

  '/api/process/update': {
    method: 'POST',
    description: '更新流程节点',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '流程节点ID' },
      name: { type: 'string', required: false, description: '节点名称' },
      code: { type: 'string', required: false, description: '节点编码' },
      order: { type: 'number', required: false, description: '排序' },
      status: { type: 'string', required: false, description: '状态' }
    },
    response: {
      success: {
        code: 200,
        message: '流程节点更新成功'
      }
    }
  },

  '/api/process/delete': {
    method: 'POST',
    description: '删除流程节点',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '流程节点ID' }
    },
    response: {
      success: {
        code: 200,
        message: '流程节点删除成功'
      }
    }
  },

  // 权限管理
  '/api/permission/list': {
    method: 'GET',
    description: '获取权限模板列表',
    auth: true,
    queryParams: {
      keyword: { type: 'string', required: false, description: '搜索关键词' },
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          items: [
            {
              id: '权限模板ID',
              name: '模板名称',
              code: '模板编码',
              permissions: '权限列表',
              status: '状态'
            }
          ]
        }
      }
    }
  },

  '/api/permission/detail': {
    method: 'GET',
    description: '获取权限模板详情',
    auth: true,
    queryParams: {
      id: { type: 'string', required: true, description: '权限模板ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '权限模板ID',
          name: '模板名称',
          code: '模板编码',
          permissions: '权限列表',
          status: '状态',
          createTime: '创建时间'
        }
      }
    }
  },

  '/api/permission/add': {
    method: 'POST',
    description: '新增权限模板',
    auth: true,
    requestBody: {
      name: { type: 'string', required: true, description: '模板名称' },
      code: { type: 'string', required: true, description: '模板编码' },
      permissions: { type: 'array', required: true, description: '权限列表' },
      status: { type: 'string', required: false, default: '正常', description: '状态' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '新权限模板ID'
        }
      }
    }
  },

  '/api/permission/update': {
    method: 'POST',
    description: '更新权限模板',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '权限模板ID' },
      name: { type: 'string', required: false, description: '模板名称' },
      code: { type: 'string', required: false, description: '模板编码' },
      permissions: { type: 'array', required: false, description: '权限列表' },
      status: { type: 'string', required: false, description: '状态' }
    },
    response: {
      success: {
        code: 200,
        message: '权限模板更新成功'
      }
    }
  },

  '/api/permission/delete': {
    method: 'POST',
    description: '删除权限模板',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '权限模板ID' }
    },
    response: {
      success: {
        code: 200,
        message: '权限模板删除成功'
      }
    }
  },

  // 业务模块管理
  '/api/module/list': {
    method: 'GET',
    description: '获取所有业务模块',
    auth: false,
    response: {
      success: {
        code: 200,
        data: [
          {
            id: '模块ID',
            name: '模块名称'
          }
        ]
      }
    },
    example: {
      request: `curl -X GET http://127.0.0.1:3007/api/module/list`,
      response: `{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "name": "订单列表"
    },
    {
      "id": "2",
      "name": "录入订单"
    }
  ],
  "success": true
}`
    }
  },

  // 图片管理
  '/api/image/upload/avatar': {
    method: 'POST',
    description: '上传头像图片',
    auth: false,
    contentType: 'multipart/form-data',
    requestBody: {
      avatar: { type: 'file', required: true, description: '头像图片文件' },
      businessType: { type: 'string', required: false, description: '业务类型' },
      businessId: { type: 'string', required: false, description: '业务ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          image: {
            id: '图片ID',
            url: '图片访问地址',
            originalName: '原始文件名',
            fileName: '存储文件名',
            fileSize: '文件大小'
          },
          message: '头像上传成功'
        }
      }
    }
  },

  '/api/image/upload/product': {
    method: 'POST',
    description: '上传商品图片',
    auth: false,
    contentType: 'multipart/form-data',
    requestBody: {
      images: { type: 'file[]', required: true, description: '商品图片文件（支持多个）' },
      businessType: { type: 'string', required: false, description: '业务类型' },
      businessId: { type: 'string', required: false, description: '业务ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          images: '图片信息数组',
          count: '上传数量',
          message: '商品图片上传成功'
        }
      }
    }
  },

  '/api/image/avatar/:fileName': {
    method: 'GET',
    description: '获取头像图片',
    auth: false,
    pathParams: {
      fileName: { type: 'string', required: true, description: '图片文件名' }
    },
    response: {
      success: {
        contentType: 'image/*',
        description: '返回图片文件流'
      },
      error: {
        code: 404,
        message: '图片不存在'
      }
    }
  },

  '/api/image/list': {
    method: 'GET',
    description: '获取图片列表',
    auth: false,
    queryParams: {
      imageType: { type: 'string', required: false, enum: ['avatar', 'product', 'follow'], description: '图片类型' },
      businessType: { type: 'string', required: false, description: '业务类型' },
      businessId: { type: 'string', required: false, description: '业务ID' },
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          page: '当前页码',
          size: '每页数量',
          items: '图片信息数组'
        }
      }
    }
  },

  '/api/image/upload/follow': {
    method: 'POST',
    description: '上传跟进图片',
    auth: false,
    contentType: 'multipart/form-data',
    requestBody: {
      images: { type: 'file[]', required: true, description: '跟进图片文件（支持多个）' },
      businessType: { type: 'string', required: false, description: '业务类型' },
      businessId: { type: 'string', required: false, description: '业务ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          images: '图片信息数组',
          count: '上传数量',
          message: '跟进图片上传成功'
        }
      }
    }
  },

  '/api/image/product/:fileName': {
    method: 'GET',
    description: '获取商品图片',
    auth: false,
    pathParams: {
      fileName: { type: 'string', required: true, description: '图片文件名' }
    },
    response: {
      success: {
        contentType: 'image/*',
        description: '返回图片文件流'
      },
      error: {
        code: 404,
        message: '图片不存在'
      }
    }
  },

  '/api/image/follow/:fileName': {
    method: 'GET',
    description: '获取跟进图片',
    auth: false,
    pathParams: {
      fileName: { type: 'string', required: true, description: '图片文件名' }
    },
    response: {
      success: {
        contentType: 'image/*',
        description: '返回图片文件流'
      },
      error: {
        code: 404,
        message: '图片不存在'
      }
    }
  },

  '/api/image/detail/:imageId': {
    method: 'GET',
    description: '根据ID获取图片信息',
    auth: false,
    pathParams: {
      imageId: { type: 'string', required: true, description: '图片ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '图片ID',
          url: '图片访问地址',
          originalName: '原始文件名',
          fileName: '存储文件名',
          fileSize: '文件大小',
          imageType: '图片类型',
          businessType: '业务类型',
          businessId: '业务ID',
          createTime: '创建时间'
        }
      }
    }
  },

  '/api/image/delete': {
    method: 'POST',
    description: '删除图片',
    auth: false,
    requestBody: {
      imageId: { type: 'string', required: true, description: '图片ID' }
    },
    response: {
      success: {
        code: 200,
        message: '图片删除成功'
      }
    }
  },

  // 商品管理
  '/api/product/list': {
    method: 'GET',
    description: '获取商品列表',
    auth: true,
    queryParams: {
      keyword: { type: 'string', required: false, description: '搜索关键词' },
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          items: [
            {
              id: '商品ID',
              name: '商品名称',
              code: '商品编码',
              category: '商品分类',
              price: '价格',
              stock: '库存',
              status: '状态'
            }
          ]
        }
      }
    }
  },

  '/api/product/add': {
    method: 'POST',
    description: '新增商品',
    auth: true,
    contentType: 'multipart/form-data',
    requestBody: {
      name: { type: 'string', required: true, description: '商品名称' },
      code: { type: 'string', required: true, description: '商品编码' },
      category: { type: 'string', required: true, description: '商品分类' },
      price: { type: 'number', required: true, description: '价格' },
      stock: { type: 'number', required: true, description: '库存' },
      description: { type: 'string', required: false, description: '商品描述' },
      images: { type: 'file[]', required: false, description: '商品图片' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '新商品ID',
          images: '上传的图片信息'
        }
      }
    }
  },

  '/api/product/detail': {
    method: 'GET',
    description: '获取商品详情',
    auth: true,
    queryParams: {
      id: { type: 'string', required: true, description: '商品ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '商品ID',
          name: '商品名称',
          code: '商品编码',
          category: '商品分类',
          price: '价格',
          stock: '库存',
          description: '商品描述',
          status: '状态',
          createTime: '创建时间'
        }
      }
    }
  },

  '/api/product/update': {
    method: 'POST',
    description: '更新商品',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '商品ID' },
      name: { type: 'string', required: false, description: '商品名称' },
      code: { type: 'string', required: false, description: '商品编码' },
      category: { type: 'string', required: false, description: '商品分类' },
      price: { type: 'number', required: false, description: '价格' },
      stock: { type: 'number', required: false, description: '库存' },
      description: { type: 'string', required: false, description: '商品描述' },
      status: { type: 'string', required: false, description: '状态' }
    },
    response: {
      success: {
        code: 200,
        message: '商品更新成功'
      }
    }
  },

  '/api/product/delete': {
    method: 'POST',
    description: '删除商品',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '商品ID' }
    },
    response: {
      success: {
        code: 200,
        message: '商品删除成功'
      }
    }
  },

  // 待办事项管理
  '/api/todo/list': {
    method: 'GET',
    description: '获取待办事项列表',
    auth: true,
    queryParams: {
      keyword: { type: 'string', required: false, description: '搜索关键词' },
      status: { type: 'string', required: false, description: '状态过滤' },
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          items: [
            {
              id: '待办事项ID',
              orderId: '订单ID',
              productName: '产品名称',
              productCode: '产品编码',
              quantity: '数量',
              departmentName: '部门名称',
              responsiblePerson: '负责人',
              startTime: '开始时间',
              endTime: '结束时间',
              processNode: '流程节点',
              status: '状态',
              remark: '备注'
            }
          ]
        }
      }
    }
  },

  '/api/todo/detail': {
    method: 'GET',
    description: '获取订单流程节点跟进详情',
    auth: true,
    queryParams: {
      id: { type: 'string', required: true, description: '待办事项ID' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '待办事项ID',
          orderId: '订单ID',
          productInfo: '产品信息',
          processInfo: '流程信息',
          followRecords: '跟进记录列表'
        }
      }
    }
  },

  '/api/todo/addRecord': {
    method: 'POST',
    description: '添加跟进记录',
    auth: true,
    requestBody: {
      todoId: { type: 'string', required: true, description: '待办事项ID' },
      content: { type: 'string', required: true, description: '跟进内容' },
      images: { type: 'array', required: false, description: '图片列表' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '新跟进记录ID'
        }
      }
    }
  },

  '/api/todo/updateStatus': {
    method: 'POST',
    description: '更新跟进状态',
    auth: true,
    requestBody: {
      id: { type: 'string', required: true, description: '待办事项ID' },
      status: { type: 'string', required: true, description: '新状态' },
      remark: { type: 'string', required: false, description: '备注' }
    },
    response: {
      success: {
        code: 200,
        message: '状态更新成功'
      }
    }
  },

  // 跟进管理
  '/api/follow/list': {
    method: 'GET',
    description: '获取跟进记录列表',
    auth: true,
    queryParams: {
      businessType: { type: 'string', required: true, enum: ['department', 'position', 'job', 'process', 'permission', 'product'], description: '业务类型' },
      businessId: { type: 'string', required: true, description: '业务ID' },
      page: { type: 'number', required: false, default: 1, description: '页码' },
      size: { type: 'number', required: false, default: 10, description: '每页数量' }
    },
    response: {
      success: {
        code: 200,
        data: {
          total: '总数量',
          items: [
            {
              id: '跟进记录ID',
              content: '跟进内容',
              status: '跟进状态',
              imageUrls: '图片地址数组',
              createTime: '创建时间',
              createUser: '创建用户'
            }
          ]
        }
      }
    }
  },

  '/api/follow/add': {
    method: 'POST',
    description: '添加跟进记录',
    auth: true,
    contentType: 'multipart/form-data',
    requestBody: {
      businessType: { type: 'string', required: true, description: '业务类型' },
      businessId: { type: 'string', required: true, description: '业务ID' },
      content: { type: 'string', required: true, description: '跟进内容' },
      status: { type: 'string', required: false, default: '待处理', description: '跟进状态' },
      remark: { type: 'string', required: false, description: '备注' },
      images: { type: 'file[]', required: false, description: '跟进图片' }
    },
    response: {
      success: {
        code: 200,
        data: {
          id: '跟进记录ID',
          imageUrls: '图片地址数组',
          images: '图片详细信息'
        }
      }
    }
  },

  // 序列号管理
  '/api/serialNumber/serialNumber': {
    method: 'GET',
    description: '获取序列号',
    auth: false,
    response: {
      success: {
        code: 200,
        data: {
          serialNumber: '生成的序列号'
        }
      }
    }
  },

  '/api/serialNumber/serialNumberByCode': {
    method: 'GET',
    description: '根据二维码获取序列号',
    auth: false,
    queryParams: {
      code: { type: 'string', required: true, description: '二维码内容' }
    },
    response: {
      success: {
        code: 200,
        data: {
          code: '二维码内容',
          serialNumber: '对应的序列号',
          productInfo: {
            name: '产品名称',
            code: '产品编码',
            category: '产品分类'
          }
        }
      }
    }
  },

  // Android专用API (不带/api前缀)
  '/departments': {
    method: 'GET',
    description: '获取部门下拉框数据(Android专用)',
    auth: false,
    response: {
      success: {
        code: 200,
        data: [
          {
            id: '部门ID',
            name: '部门名称',
            code: '部门编码'
          }
        ]
      }
    },
    example: {
      request: `curl -X GET http://********:3007/departments`,
      response: `{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "name": "研发部",
      "code": "RD001"
    },
    {
      "id": "2",
      "name": "市场部",
      "code": "MK001"
    }
  ],
  "success": true
}`
    }
  },

  '/positions': {
    method: 'GET',
    description: '获取职位下拉框数据(Android专用)',
    auth: false,
    response: {
      success: {
        code: 200,
        data: [
          {
            id: '职位ID',
            name: '职位名称',
            code: '职位编码'
          }
        ]
      }
    },
    example: {
      request: `curl -X GET http://********:3007/positions`,
      response: `{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "name": "工程师",
      "code": "ENG001"
    },
    {
      "id": "2",
      "name": "经理",
      "code": "MGR001"
    }
  ],
  "success": true
}`
    }
  },

  '/posts': {
    method: 'GET',
    description: '获取岗位下拉框数据(Android专用)',
    auth: false,
    response: {
      success: {
        code: 200,
        data: [
          {
            id: '岗位ID',
            name: '岗位名称',
            code: '岗位编码'
          }
        ]
      }
    },
    example: {
      request: `curl -X GET http://********:3007/posts`,
      response: `{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "name": "前端开发",
      "code": "FE001"
    },
    {
      "id": "2",
      "name": "后端开发",
      "code": "BE001"
    }
  ],
  "success": true
}`
    }
  },

  '/permissions': {
    method: 'GET',
    description: '获取权限模板下拉框数据(Android专用)',
    auth: false,
    response: {
      success: {
        code: 200,
        data: [
          {
            id: '权限模板ID',
            name: '模板名称',
            code: '模板编码'
          }
        ]
      }
    },
    example: {
      request: `curl -X GET http://********:3007/permissions`,
      response: `{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "name": "管理员权限",
      "code": "ADMIN001"
    },
    {
      "id": "2",
      "name": "普通用户权限",
      "code": "USER001"
    }
  ],
  "success": true
}`
    }
  },

  '/processes': {
    method: 'GET',
    description: '获取流程节点下拉框数据(Android专用)',
    auth: false,
    response: {
      success: {
        code: 200,
        data: [
          {
            id: '流程节点ID',
            name: '节点名称',
            code: '节点编码'
          }
        ]
      }
    },
    example: {
      request: `curl -X GET http://********:3007/processes`,
      response: `{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "1",
      "name": "下料",
      "code": "PROC001"
    },
    {
      "id": "2",
      "name": "加工",
      "code": "PROC002"
    }
  ],
  "success": true
}`
    }
  }
};

module.exports = apiSpec;
