{"_from": "address@^1.0.1", "_id": "address@1.2.2", "_inBundle": false, "_integrity": "sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA==", "_location": "/address", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "address@^1.0.1", "name": "address", "escapedName": "address", "rawSpec": "^1.0.1", "saveSpec": null, "fetchSpec": "^1.0.1"}, "_requiredBy": ["/detect-port"], "_resolved": "https://registry.npmjs.org/address/-/address-1.2.2.tgz", "_shasum": "2b5248dac5485a6390532c6a517fda2e3faac89e", "_spec": "address@^1.0.1", "_where": "D:\\Code\\node-express\\node_modules\\detect-port", "author": {"name": "fengmk2", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/node-modules/address/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Get current machine IP, MAC and DNS servers.", "devDependencies": {"@types/node": "14", "egg-bin": "^5.6.1", "eslint": "^8.30.0", "eslint-config-egg": "^12.1.0", "git-contributor": "^1.1.0", "mm": "*", "runscript": "^1.4.0", "typescript": "4"}, "engines": {"node": ">= 10.0.0"}, "files": ["lib"], "homepage": "https://github.com/node-modules/address#readme", "keywords": ["address", "ip", "ipv4", "mac"], "license": "MIT", "main": "lib/address.js", "name": "address", "repository": {"type": "git", "url": "git://github.com/node-modules/address.git"}, "scripts": {"ci": "egg-bin cov", "contributors": "git-contributor", "lint": "eslint test", "test": "egg-bin test"}, "types": "lib/address.d.ts", "version": "1.2.2"}