{"_from": "@sideway/formula@^3.0.1", "_id": "@sideway/formula@3.0.1", "_inBundle": false, "_integrity": "sha512-/poHZJJVjx3L+zVD6g9KgHfYnb443oi7wLu/XKojDviHy6HOEOA6z1Trk5aR1dGcmPenJEgb2sK2I80LeS3MIg==", "_location": "/@sideway/formula", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@sideway/formula@^3.0.1", "name": "@sideway/formula", "escapedName": "@sideway%2fformula", "scope": "@sideway", "rawSpec": "^3.0.1", "saveSpec": null, "fetchSpec": "^3.0.1"}, "_requiredBy": ["/joi"], "_resolved": "https://registry.npmjs.org/@sideway/formula/-/formula-3.0.1.tgz", "_shasum": "80fcbcbaf7ce031e0ef2dd29b1bfc7c3f583611f", "_spec": "@sideway/formula@^3.0.1", "_where": "D:\\Code\\node-express\\node_modules\\joi", "bugs": {"url": "https://github.com/sideway/formula/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Math and string formula parser.", "devDependencies": {"@hapi/code": "8.x.x", "@hapi/lab": "24.x.x", "typescript": "4.0.x"}, "files": ["lib"], "homepage": "https://github.com/sideway/formula#readme", "keywords": ["formula", "parser", "math", "string"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@sideway/formula", "repository": {"type": "git", "url": "git://github.com/sideway/formula.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "types": "lib/index.d.ts", "version": "3.0.1"}