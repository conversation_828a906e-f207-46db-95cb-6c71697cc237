{"_from": "@hapi/formula@^2.0.0", "_id": "@hapi/formula@2.0.0", "_inBundle": false, "_integrity": "sha512-V87P8fv7PI0LH7LiVi8Lkf3x+KCO7pQozXRssAHNXXL9L1K+uyu4XypLXwxqVDKgyQai6qj3/KteNlrqDx4W5A==", "_location": "/@hapi/formula", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@hapi/formula@^2.0.0", "name": "@hapi/formula", "escapedName": "@hapi%2fformula", "scope": "@hapi", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/@hapi/joi"], "_resolved": "https://registry.npmjs.org/@hapi/formula/-/formula-2.0.0.tgz", "_shasum": "edade0619ed58c8e4f164f233cda70211e787128", "_spec": "@hapi/formula@^2.0.0", "_where": "D:\\Code\\node-express\\node_modules\\@hapi\\joi", "bugs": {"url": "https://github.com/hapijs/formula/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": "Moved to 'npm install @sideway/formula'", "description": "Math and string formula parser.", "devDependencies": {"@hapi/code": "6.x.x", "@hapi/lab": "20.x.x"}, "files": ["lib"], "homepage": "https://github.com/hapijs/formula#readme", "keywords": ["formula", "parser", "math", "string"], "license": "BSD-3-<PERSON><PERSON>", "main": "lib/index.js", "name": "@hapi/formula", "repository": {"type": "git", "url": "git://github.com/hapijs/formula.git"}, "scripts": {"test": "lab -a @hapi/code -t 100 -L -Y", "test-cov-html": "lab -a @hapi/code -t 100 -L -r html -o coverage.html"}, "types": "lib/index.d.ts", "version": "2.0.0"}