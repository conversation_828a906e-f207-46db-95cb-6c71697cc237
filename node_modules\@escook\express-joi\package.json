{"_from": "@escook/express-joi@^1.1.1", "_id": "@escook/express-joi@1.1.1", "_inBundle": false, "_integrity": "sha512-s<PERSON>zrugjkjOfAUG5cKMLl8lvgkbvfJzFWIdDBA9uqeI8VF7Dx39k3NhgK2QOkFJVLORZDbKaZpqWekSp2qNqNQg==", "_location": "/@escook/express-joi", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@escook/express-joi@^1.1.1", "name": "@escook/express-joi", "escapedName": "@escook%2fexpress-joi", "scope": "@escook", "rawSpec": "^1.1.1", "saveSpec": null, "fetchSpec": "^1.1.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/@escook/express-joi/-/express-joi-1.1.1.tgz", "_shasum": "1a8beb8857c1cbbc035ff956e085d2bf13d8f87c", "_spec": "@escook/express-joi@^1.1.1", "_where": "D:\\Code\\node-express", "author": {"name": "LiuLongBin"}, "bugs": {"url": "https://github.com/liulongbin1314/express-joi/issues"}, "bundleDependencies": false, "dependencies": {"joi": "^17.4.0"}, "deprecated": false, "description": "基于 @hapi/joi，用于校验 body、query 和 params 的 express 中间件。", "homepage": "https://github.com/liulongbin1314/express-joi#readme", "keywords": ["express", "joi", "express-joi", "validator"], "license": "ISC", "main": "index.js", "name": "@escook/express-joi", "repository": {"type": "git", "url": "git+ssh://**************/liulongbin1314/express-joi.git"}, "scripts": {}, "version": "1.1.1"}