{"_from": "async@^1.5.0", "_id": "async@1.5.2", "_inBundle": false, "_integrity": "sha512-nSVgobk4rv61R9PUSDtYt7mPVB2olxNR5RWJcAsH676/ef11bUZwvu7+RGYrYauVdDPcO519v68wRhXQtxsV9w==", "_location": "/async", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "async@^1.5.0", "name": "async", "escapedName": "async", "rawSpec": "^1.5.0", "saveSpec": null, "fetchSpec": "^1.5.0"}, "_requiredBy": ["/express-jwt"], "_resolved": "https://registry.npmjs.org/async/-/async-1.5.2.tgz", "_shasum": "ec6a61ae56480c0c3cb241c95618e20892f9672a", "_spec": "async@^1.5.0", "_where": "D:\\Code\\node-express\\node_modules\\express-jwt", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/caolan/async/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Higher-order functions and common patterns for asynchronous code", "devDependencies": {"benchmark": "github:bestiejs/benchmark.js", "bluebird": "^2.9.32", "chai": "^3.1.0", "coveralls": "^2.11.2", "es6-promise": "^2.3.0", "jscs": "^1.13.1", "jshint": "~2.8.0", "karma": "^0.13.2", "karma-browserify": "^4.2.1", "karma-firefox-launcher": "^0.1.6", "karma-mocha": "^0.2.0", "karma-mocha-reporter": "^1.0.2", "lodash": "^3.9.0", "mkdirp": "~0.5.1", "mocha": "^2.2.5", "native-promise-only": "^0.8.0-a", "nodeunit": ">0.0.0", "nyc": "^2.1.0", "rsvp": "^3.0.18", "semver": "^4.3.6", "uglify-js": "~2.4.0", "xyz": "^0.5.0", "yargs": "~3.9.1"}, "files": ["lib", "dist/async.js", "dist/async.min.js"], "homepage": "https://github.com/caolan/async#readme", "jam": {"main": "lib/async.js", "include": ["lib/async.js", "README.md", "LICENSE"], "categories": ["Utilities"]}, "keywords": ["async", "callback", "utility", "module"], "license": "MIT", "main": "lib/async.js", "name": "async", "repository": {"type": "git", "url": "git+https://github.com/caolan/async.git"}, "scripts": {"coverage": "nyc npm test && nyc report", "coveralls": "nyc npm test && nyc report --reporter=text-lcov | coveralls", "lint": "jshint lib/*.js test/*.js perf/*.js && jscs lib/*.js test/*.js perf/*.js", "mocha-browser-test": "karma start", "mocha-node-test": "mocha mocha_test/", "mocha-test": "npm run mocha-node-test && npm run mocha-browser-test", "nodeunit-test": "nodeunit test/test-async.js", "test": "npm run-script lint && npm run nodeunit-test && npm run mocha-test"}, "spm": {"main": "lib/async.js"}, "version": "1.5.2", "volo": {"main": "lib/async.js", "ignore": ["**/.*", "node_modules", "bower_components", "test", "tests"]}}