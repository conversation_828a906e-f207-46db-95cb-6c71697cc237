{"_from": "blob@0.0.4", "_id": "blob@0.0.4", "_inBundle": false, "_integrity": "sha512-YRc9zvVz4wNaxcXmiSgb9LAg7YYwqQ2xd0Sj6osfA7k/PKmIGVlnOYs3wOFdkRC9/JpQu8sGt/zHgJV7xzerfg==", "_location": "/blob", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "blob@0.0.4", "name": "blob", "escapedName": "blob", "rawSpec": "0.0.4", "saveSpec": null, "fetchSpec": "0.0.4"}, "_requiredBy": ["/engine.io-parser"], "_resolved": "https://registry.npmjs.org/blob/-/blob-0.0.4.tgz", "_shasum": "bcf13052ca54463f30f9fc7e95b9a47630a94921", "_spec": "blob@0.0.4", "_where": "D:\\Code\\node-express\\node_modules\\engine.io-parser", "bugs": {"url": "https://github.com/rase-/blob/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Abstracts out Blob and uses BlobBulder in cases where it is supported with any vendor prefix.", "devDependencies": {"browserify": "3.30.1", "expect.js": "0.2.0", "mocha": "1.17.1", "zuul": "1.5.4"}, "homepage": "https://github.com/rase-/blob", "name": "blob", "repository": {"type": "git", "url": "git+ssh://**************/rase-/blob.git"}, "scripts": {"test": "make test"}, "version": "0.0.4"}