const { func } = require('joi');
const db = require('../db/index')
const log = require('./log')
// 获取权限模板列表
const getPermissionList = (req, res) => {
  try {
    const user = req.user;
    const { keyword = '', page = 1, size = 10 } = req.query;
    console.log("查询参数:", { page, size, keyword });

    // 构建SQL查询
    let extSql = "";
    let params = [];

    if (keyword && keyword.trim() !== '') {
      extSql = "AND (`name` LIKE ? OR 'code' LIKE ?) ";
      const searchTerm = `%${keyword}%`;
      params = [searchTerm, searchTerm, searchTerm];
    }

    // 计算分页
    const offset = (page - 1) * size;

    // 查询总数
    const countSql = "SELECT COUNT(*) as total FROM permissions WHERE  `status` = 1 " + extSql;
    db.query(countSql, params, (countErr, countResults) => {
      if (countErr) {
        console.error("查询总数出错:", countErr);
        return res.output(countErr, 500);
      }

      const total = countResults[0].total;

      // 查询数据
      const dataSql = "SELECT * FROM permissions WHERE `status` = 1 " + extSql + " LIMIT ?, ?";
      const dataParams = [...params, offset, size];
      db.query(dataSql, dataParams, (err, results) => {
        if (err) {
          console.error("查询数据出错:", err);
          return res.output(err, 500);
        }

        // 返回结果
        res.output(null, 200, {
          total: total,
          page: page,
          size: size,
          list: results
        }, true);
      });
    });
    // 记录日志
    log.add("sysLog", "查询权限模板列表:" + keyword, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取权限模板详情
const getPermissionDetail = (req, res) => {
  try {
    const { id } = req.query;
    const sql = 'select * from permissions  where id= ?'

    db.query(sql, id, (err, results) => {
      if (err) return res.output(err)
      if (results.length !== 1) return res.output(new Error("获取权限模板信息失败！"), 400)
      const permission = { ...results[0] };
      if (permission.code) {
        const sqlStr = 'select * from permission_items where parentCode =?'
        db.query(sqlStr, permission.code, (err, itemResults) => {
          if (err) return res.output(err)
          res.output(null, 200, { permission: permission, modules: itemResults }, true);
        })
      } else {
        res.output(null, 200, { permission: permission, modules: {} }, true);
      }
    })
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 新增权限模板
const addPermission = (req, res) => {
  try {
    const user = req.user;
    const { name, code, status, modules, description } = req.body;

    const sqlStr = "insert into permissions set ?";
    db.query(sqlStr, { name, code, status, description }, (err, results) => {
      if (err) return res.output(err);
      if (results.affectedRows !== 1) return res.output(new Error("新增权限模板失败！"), 400);
      addPermissionItem(code, modules);
      res.output(null, 200, { id: results.insertId,message: "新增权限模板成功" });
    });
    log.add("sysLog", "新增权限模板信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 更新权限模板
const updatePermission = (req, res) => {
  try {
    const user = req.user;
    const { id, name, code, status, modules, description } = req.body;
    const sqlStr = 'update permissions set name=?,code=?,status=?,description=? where id =?;'
    db.query(sqlStr, [name, code, status, description, id], (err, results) => {
      if (err) return res.output(err)
      deletePermissionItem(code);
      addPermissionItem(code, modules);
      res.output(null, 200, { id: id, name: name, code: code }, true)
    })
    log.add("sysLog", "更新权限模板信息:" + name, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 删除权限模板
const deletePermission = (req, res) => {
  try {
    const user = req.user;
    const { id } = req.body;
    const sqlStr = 'update permissions set delFlag = 1 where id =?'
    db.query(sqlStr, id, (err, results) => {
      if (err) return res.output(err)
      if (results.affectedRows !== 1) return res.output(new Error('删除权限模板信息失败！'), 400)
      res.output(null, 200, { id: id }, true)
    })
    log.add("sysLog", "删除权限模板信息:" + id, user.username);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// Android应用专用：获取职位下拉框数据
const getPermissions = (req, res) => {
  try {
    // 查询数据
    const dataSql = "SELECT * FROM permissions WHERE delFlag=0 ";
    db.query(dataSql, null, (err, results) => {
      if (err) {
        console.error("查询数据出错:", err);
        return res.output(err, 500);
      }
      // 返回结果
      res.output(null, 200, results, true);
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

function deletePermissionItem(parentCode) {
  const sqlStr = 'delete from permission_items where parentCode =?;'
  db.query(sqlStr, parentCode, (err, results) => {
    if (err) return res.output(err)
    log.add("sysLog", "删除权限Code:" + parentCode, "system");
  })
}

function addPermissionItem(parentCode, modules) {
  for (let i = 0; i < modules.length; i++) {
    let module = modules[i];
    let code = module.code;   
    const sqlStr = 'insert into permission_items set ?;'
    db.query(sqlStr, { parentCode, code }, (err, results) => {
      if (err) return res.output(err)    
    })
  }
}

module.exports = {
  getPermissionList,
  getPermissionDetail,
  addPermission,
  updatePermission,
  deletePermission,
  getPermissions
};
