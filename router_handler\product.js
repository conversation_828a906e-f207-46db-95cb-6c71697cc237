const db = require('../db/index')
const log = require('./log')

// 获取商品列表
const getProductList = (req, res) => {
  try {
    const user = req.user;

    // 获取查询参数
    const page = parseInt(req.query.page) || 1;
    const size = parseInt(req.query.size) || 10;
    const keyword = req.query.keyword || '';

    console.log("查询参数:", { page, size, keyword });

    // 构建SQL查询
    let extSql = "";
    let params = [];

    if (keyword && keyword.trim() !== '') {
      extSql = "AND (`code` LIKE ? OR `name` LIKE ? OR `model` LIKE ? OR 'standard' LIKE ? ) ";
      const searchTerm = `%${keyword}%`;
      params = [searchTerm, searchTerm, searchTerm, searchTerm];
    }

    // 计算分页
    const offset = (page - 1) * size;

    // 查询总数
    const countSql = "SELECT COUNT(*) as total FROM products WHERE delFlag = 0  " + extSql;

    db.query(countSql, params, (countErr, countResults) => {
      if (countErr) {
        console.error("查询总数出错:", countErr);
        return res.output(countErr, 500);
      }

      const total = countResults[0].total;

      // 查询数据
      const dataSql = "SELECT	* FROM products "
        + "WHERE delFlag = 0 "
        + extSql + " LIMIT ?, ?";
      const dataParams = [...params, offset, size];

      db.query(dataSql, dataParams, (err, results) => {
        if (err) {
          console.error("查询数据出错:", err);
          return res.output(err, 500);
        }

        // 返回结果
        res.output(null, 200, {
          total: total,
          page: page,
          size: size,
          list: results
        });
      });
    });
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 获取商品详情
const getProductDetail = (req, res) => {
  try {
    const { id } = req.query;
    const sql = 'select * from products where id= ?'

    db.query(sql, id, (err, results) => {
      if (err) return res.output(err)
      if (results.length !== 1) return res.output(new Error("获取商品信息失败！"), 400)
      const product = { ...results[0] };
      if (product.id) {
        const sqlStr = "SELECT pc.number,pc.status,cs.* FROM `product_composition` pc "
          + "LEFT JOIN components cs ON pc.componentId = cs.id "
          + "WHERE	pc.productId = ?"
        db.query(sqlStr, product.id, (err, itemResults) => {
          if (err) return res.output(err)
          res.output(null, 200, { product: product, componentList: itemResults }, true);
        })
      } else {
        res.output(null, 200, { product: product, componentList: {} }, true);
      }
    })
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

// 新增商品
const addProduct = (req, res) => {
  const { IMAGE_TYPES, createUploader, handleFileUpload } = require('../utils/imageManager');
  const uploader = createUploader(IMAGE_TYPES.PRODUCT, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    try {
      const { name, code, category, price, stock, status, description, specifications } = req.body;
      const files = req.files || [];
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      // 模拟新增操作
      const newId = Date.now().toString();

      let savedImages = [];
      if (files.length > 0) {
        savedImages = await handleFileUpload(files, IMAGE_TYPES.PRODUCT, {
          businessType: 'product',
          businessId: newId,
          uploadUserId: uploadUserId,
          uploadUserName: uploadUserName
        });
      }

      res.output(null, 200, {
        id: newId,
        images: savedImages
      }, true);
    } catch (error) {
      res.output(error, 500, null, false);
    }
  });
};

// 更新商品
const updateProduct = (req, res) => {
  const { IMAGE_TYPES, createUploader, handleFileUpload } = require('../utils/imageManager');
  const uploader = createUploader(IMAGE_TYPES.PRODUCT, 10);

  uploader.array('images', 10)(req, res, async function (err) {
    if (err) {
      return res.output(err, 400, null, false);
    }

    try {
      const { id, name, code, category, price, stock, status, description, specifications, deleteImageIds } = req.body;
      const files = req.files || [];
      const uploadUserId = req.user ? req.user.id : null;
      const uploadUserName = req.user ? req.user.username : null;

      let savedImages = [];
      if (files.length > 0) {
        savedImages = await handleFileUpload(files, IMAGE_TYPES.PRODUCT, {
          businessType: 'product',
          businessId: id,
          uploadUserId: uploadUserId,
          uploadUserName: uploadUserName
        });
      }

      // 处理删除的图片
      if (deleteImageIds && Array.isArray(deleteImageIds)) {
        const { deleteImage } = require('../utils/imageManager');
        for (const imageId of deleteImageIds) {
          try {
            await deleteImage(imageId);
          } catch (error) {
            console.error('删除商品图片失败:', imageId, error);
          }
        }
      }

      res.output(null, 200, {
        id: id,
        newImages: savedImages,
        deletedImageIds: deleteImageIds || []
      }, true);
    } catch (error) {
      res.output(error, 500, null, false);
    }
  });
};

// 删除商品
const deleteProduct = (req, res) => {
  try {
    const { id } = req.body;

    // 模拟删除操作
    res.output(null, 200, null, true);
  } catch (error) {
    res.output(error, 500, null, false);
  }
};

module.exports = {
  getProductList,
  getProductDetail,
  addProduct,
  updateProduct,
  deleteProduct
};
