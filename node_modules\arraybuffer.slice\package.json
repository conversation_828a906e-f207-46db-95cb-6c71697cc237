{"_from": "arraybuffer.slice@0.0.6", "_id": "arraybuffer.slice@0.0.6", "_inBundle": false, "_integrity": "sha512-6ZjfQaBSy6CuIH0+B0NrxMfDE5VIOCP/5gOqSpEIsaAZx9/giszzrXg6PZ7G51U/n88UmlAgYLNQ9wAnII7PJA==", "_location": "/arraybuffer.slice", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "arraybuffer.slice@0.0.6", "name": "arraybuffer.slice", "escapedName": "arraybuffer.slice", "rawSpec": "0.0.6", "saveSpec": null, "fetchSpec": "0.0.6"}, "_requiredBy": ["/engine.io-parser"], "_resolved": "https://registry.npmjs.org/arraybuffer.slice/-/arraybuffer.slice-0.0.6.tgz", "_shasum": "f33b2159f0532a3f3107a272c0ccfbd1ad2979ca", "_spec": "arraybuffer.slice@0.0.6", "_where": "D:\\Code\\node-express\\node_modules\\engine.io-parser", "bugs": {"url": "https://github.com/rase-/arraybuffer.slice/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Exports a function for slicing ArrayBuffers (no polyfilling)", "devDependencies": {"expect.js": "0.2.0", "mocha": "1.17.1"}, "homepage": "https://github.com/rase-/arraybuffer.slice", "name": "arraybuffer.slice", "repository": {"type": "git", "url": "git+ssh://**************/rase-/arraybuffer.slice.git"}, "version": "0.0.6"}